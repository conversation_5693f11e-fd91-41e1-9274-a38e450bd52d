<configuration>
  <property resource="application.conf" />

  <conversionRule conversionWord="coloredLevel" converterClass="play.api.Logger$ColoredLevel" />

  <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>%date - %thread - %level - %logger - %message%n%xException{5}</pattern>
    </encoder>
  </appender>

  <appender name="PAPERTRAIL" class="ch.qos.logback.classic.net.SyslogAppender">
    <syslogHost>${papertrail.logger.host}</syslogHost>
    <port>${papertrail.logger.port}</port>
    <facility>USER</facility>
    <suffixPattern>lm_app-${papertrail.logger.env}: %level - %logger{15} - %message - %replace(%xException{5}){'\n',''}%nopex%n</suffixPattern>
  </appender>

  <!--
    The logger name is typically the Java/Scala package name.
    This configures the log level to log at for a package and its children packages.
  -->
  <logger name="play" level="DEBUG" />
  <logger name="application" level="DEBUG" />
  <!--
    Reduce log noise in NewRelic by setting these packages to ERROR.
    If further suppression is needed, consider setting the level to OFF.
  -->
  <!-- Silence all com.sun.mail.* logs -->
  <logger name="com.sun.mail" level="ERROR" />
  <!-- Silence all com.sun.metro.* logs -->
  <logger name="com.sun.metro" level="ERROR" />
  <!-- Silence all com.sun.xml.internal.* logs -->
  <logger name="com.sun.xml.internal.bind" level="ERROR" />
  <logger name="com.sun.xml.internal.ws" level="ERROR" />
  <!-- Silence all javax.* logs -->
  <logger name="javax.activation" level="ERROR" />
  <logger name="javax.mail" level="ERROR" />
  <logger name="javax.management" level="ERROR" />
  <logger name="javax.xml" level="ERROR" />
  <!-- Silence all org.eclipse.paho.client.mqttv3.* logs -->
  <logger name="org.eclipse.paho.client.mqttv3" level="ERROR" />
  <!-- Silence sun.net.www.protocol.http.* logs -->
  <logger name="sun.net.www.protocol.http" level="ERROR" />

  <root level="ERROR">
    <appender-ref ref="STDOUT" />
    <appender-ref ref="PAPERTRAIL" />
  </root>

</configuration>
