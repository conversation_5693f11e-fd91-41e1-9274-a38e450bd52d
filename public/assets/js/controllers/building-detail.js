app.controller('BuildingDetailController', [
  '$scope',
  '$window',
  '$aside',
  '$location',
  '$modal',
  'BuildingService',
  'PartsService',
  'building',
  'MaintenanceService',
  'UserService',
  'CopyService',
  'TinyUrlService',
  'CardService',
  function (
    $scope,
    $window,
    $aside,
    $location,
    $modal,
    buildingService,
    partsService,
    building,
    maintenanceService,
    userService,
    copyService,
    tinyUrlService,
    cardService
  ) {
    $scope.activeTab = -1

    $scope.building = building.data
    $scope.technicians = []
    $scope.machines = []
    $scope.units = []
    $scope.maintenances = []

    $scope.data = {
      file: null,
    }

    $scope.building.showPaymentMethod =
      $scope.building.contractType !== 'POSTPAID'

    $scope.toast = {
      text: '',
      type: '',
    }

    $scope.paymentMethods = [
      { key: 'NOT_SPECIFIED', value: 'No especificado' },
      { key: 'WEB_MP_ONLY', value: 'Web solo MP' },
      { key: 'WEB_AND_REDPAGOS', value: 'Web + Redpagos' },
    ]

    $scope.buildingClosureTypes = {
      POSTPAID: 'POSTPAGO',
      POSTPAID_WITH_MINIMUM: 'POSTPAGO-CON-MÍNIMO',
      POSTPAID_WITH_MINIMUM_PER_UNIT: 'POSTPAGO-CON-MINIMO-POR-UNIDAD',
      POSTPAID_RESOURCE_REIMBURSEMENT: 'POSTPAGO-CON-RR',
      POSTPAID_WITH_MINIMUM_RESOURCE_REIMBURSEMENT: 'POSTPAGO-CON-MÍNIMO-Y-RR',
      POSTPAID_WITH_MINIMUM_PER_UNIT_RESOURCE_REIMBURSEMENT:
        'POSTPAGO-CON-MÍNIMO-POR-UNIDAD-Y-RR',
      PREPAID: 'PREPAGO',
      PREPAID_WITH_MINIMUM_PER_UNIT: 'PREPAGO-CON-MÍNIMO-POR-UNIDAD',
      PREPAID_AUTO_RECHARGEABLE_USES: 'PREPAGO-USOS-AUTO-RECARGADOS',
      PREPAID_RESOURCE_REIMBURSEMENT: 'PREPAGO-CON-RR',
      PREPAID_WITH_MINIMUM_PER_UNIT_RESOURCE_REIMBURSEMENT:
        'PREPAGO-CON-MÍNIMO-POR-UNIDAD-CON-RR',
      COLIVING: 'COLIVING',
      MIXED: 'MIXTO',
      MIXED_RESOURCE_REIMBURSEMENT: 'MIXTO-CON-RR',
      MIXED_AUTO_RECHARGEABLE_USES: 'MIXTO-CON-USOS-AUTO-RECARGADOS',
    }

    //settings
    $scope.settings = {
      singular: 'Item',
      plural: 'Items',
      cmd: 'Add',
    }

    //defining template
    var formTpl = $aside({
      scope: $scope,
      template: '/unit-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    var usesTpl = $aside({
      scope: $scope,
      template: '/uses-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    var buildingFileTpl = $aside({
      scope: $scope,
      template: '/building-file-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    var maintenanceFormTpl = $aside({
      scope: $scope,
      template: '/maintenance-form.html',
      show: false,
      placement: 'left',
      backdrop: false,
      animation: 'am-slide-left',
    })

    $scope.filters = {
      maintenanceOrderBy: 'timestamp',
      unitsOrderBy: 'tower',
      machinesOrderBy: 'sort_index',
      maintenanceReverse: true,
    }

    // methods
    $scope.enableAdministrationClosure = function (building) {
      if (confirm(`Está seguro que desea Habilitar el Cierre?`)) {
        buildingService
          .enableAdministrationClosurePermission(building.id)
          .then(function (response) {
            $scope.building.isIncludedInClosure = true
          })
      }
    }

    $scope.disableAdministrationClosure = function (building) {
      if (confirm(`Está seguro que desea Deshabilitar el Cierre?`)) {
        buildingService
          .disableAdministrationClosurePermission(building.id)
          .then(function (response) {
            $scope.building.isIncludedInClosure = false
          })
      }
    }

    $scope.enableForMaintenance = function (building) {
      buildingService
        .enableForMaintenance(building.id)
        .then(function (response) {
          $scope.building.isIncludedInClosure = true
        })
    }

    $scope.disableForMaintenance = function (building) {
      buildingService
        .disableForMaintenance(building.id)
        .then(function (response) {
          $scope.building.isIncludedInClosure = false
        })
    }

    $scope.enablePagosWebWithSplit = function (building) {
      buildingService
        .enablePagosWebWithSplit(building.id)
        .then(function (response) {
          $scope.building.isPagosWebWithSplitEnabled = true
        })
    }

    $scope.disablePagosWebWithSplit = function (building) {
      buildingService
        .disablePagosWebWithSplit(building.id)
        .then(function (response) {
          $scope.building.isPagosWebWithSplitEnabled = false
        })
    }

    $scope.enableRedPagosWithSplit = function (building) {
      buildingService
        .enableRedPagosWithSplit(building.id)
        .then(function (response) {
          $scope.building.isRedPagosWithSplitEnabled = true
        })
    }

    $scope.disableRedPagosWithSplit = function (building) {
      buildingService
        .disableRedPagosWithSplit(building.id)
        .then(function (response) {
          $scope.building.isRedPagosWithSplitEnabled = false
        })
    }

    $scope.orderMaintenanceColumn = function (order) {
      $scope.filters.maintenanceReverse =
        order != $scope.filters.maintenanceOrderBy
          ? false
          : !$scope.filters.maintenanceReverse
      $scope.filters.maintenanceOrderBy = order
    }

    $scope.orderUnitsColumn = function (order) {
      $scope.filters.unitsReverse =
        order != $scope.filters.unitsOrderBy
          ? false
          : !$scope.filters.unitsReverse
      $scope.filters.unitsOrderBy = order
    }

    $scope.orderMachineColumn = function (order) {
      $scope.filters.machineReverse =
        order != $scope.filters.machinesOrderBy
          ? false
          : !$scope.filters.machineReverse
      $scope.filters.machinesOrderBy = order
    }

    $scope.loadTechnicians = function () {
      if ($scope.technicians.length) {
        return
      }
      $scope.loadingTechnicians = userService
        .loadTechnicians()
        .then(function (data) {
          $scope.technicians = userService
            .getTechnicians()
            .map((t) => t.name + ' ' + t.lastname)
        })
    }

    $scope.loadMaintenances = function (reload) {
      if ($scope.maintenances.length && !reload) {
        return
      }

      $scope.loadingMaintenances = buildingService
        .loadMaintenances($scope.building.id)
        .then(function (data) {
          $scope.maintenances = buildingService.getMaintenances()
        })
    }

    $scope.loadUnits = function (reload) {
      if ($scope.units.length && !reload) {
        return
      }

      $scope.loadingUnits = buildingService
        .loadUnits($scope.building.id)
        .then(function (data) {
          $scope.units = buildingService
            .getUnits()
            .sortBy('tower', 'number', true)
        })
    }

    $scope.loadMachines = function (reload) {
      if ($scope.machines.length && !reload) {
        return
      }

      $scope.loadingMachines = buildingService
        .loadMachines($scope.building.id)
        .then(function (data) {
          $scope.machines = buildingService.getMachines()
        })
    }

    $scope.checkAll = function () {
      angular.forEach($scope.data, function (item) {
        item.selected = !item.selected
      })
    }

    $scope.editUnit = function (item) {
      if (item) {
        item.editing = true
        $scope.item = item
        $scope.settings.cmd = 'Edit'
        $scope.showForm()
      }
    }

    $scope.viewItem = function (item) {
      if (item) {
        item.editing = false
        $scope.item = item
        $scope.settings.cmd = 'View'
        $scope.showForm()
      }
    }

    $scope.setActiveTab = function (tabIndex) {
      $scope.activeTab = tabIndex
      switch (tabIndex) {
        case 0:
          return $scope.loadUnits()
        case 1:
          return $scope.loadMachines()
        case 2:
          $scope.loadTechnicians()
          return $scope.loadMaintenances()
      }
    }

    $scope.createUnit = function () {
      var item = {
        editing: true,
      }
      $scope.item = item
      $scope.settings.cmd = 'New'
      $scope.showForm()
    }

    $scope.saveItem = function (item) {
      if ($scope.settings.cmd == 'New') {
        $scope.errorMessage = null

        $scope.savingUnit = buildingService
          .createUnit($scope.building.id, $scope.item)
          .success(function () {
            $scope.hideForm()
            $scope.loadUnits(true)
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido crear la unidad. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      } else if ($scope.settings.cmd == 'Edit') {
        $scope.errorMessage = null

        $scope.savingUnit = buildingService
          .updateUnit($scope.building.id, item.id, item)
          .success(function () {
            $scope.hideForm()
            $scope.loadUnits(true)
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido modificar la unidad. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      }
    }

    $scope.deleteUnit = function (item) {
      if (confirm('Are you sure?')) {
        buildingService
          .deleteUnit($scope.building.id, item.id)
          .success(function () {
            $scope.loadUnits(true)
          })
      }
    }

    $scope.showLoadUses = function () {
      $scope.showUsesForm()
    }

    $scope.showLoadBuidling = function () {
      $scope.showBuildingForm()
    }

    $scope.showForm = function () {
      angular.element('.tooltip').remove()
      formTpl.show()
    }

    $scope.hideForm = function () {
      formTpl.hide()
    }

    $scope.showUsesForm = function () {
      angular.element('.tooltip').remove()
      usesTpl.show()
    }

    $scope.showBuildingForm = function () {
      angular.element('.tooltip').remove()
      buildingFileTpl.show()
    }

    $scope.hideUsesForm = function () {
      usesTpl.hide()
    }

    $scope.hideBuidlingFile = function () {
      buildingFileTpl.hide()
    }

    $scope.$on('$destroy', function () {
      $scope.hideForm()
    })

    $scope.uploadFile = function (file) {
      $scope.uploadingUses = buildingService
        .uploadingUses(file, $scope.building.id, $scope.username)
        .then(
          function (response) {
            $scope.hideUsesForm()
            $scope.loadUnits(true)
            alert(
              'Carga finalizada. Procesados ok: ' +
                response.data.processed_ok +
                ', No Procesados: ' +
                response.data.processed_error
            )
          },
          function (response) {
            if (response.status > 0)
              $scope.errorMessage =
                'No se ha podido cargar el archivo. ' + reason.result_detail
                  ? reason.result_detail
                  : ''
          }
        )
    }

    $scope.uploadBuildingFile = function (file) {
      $scope.uploadingUses = buildingService
        .uploadBuildingFile(file, $scope.building.id, $scope.username)
        .then(
          function (response) {
            $scope.hideBuidlingFile()
            $scope.loadUnits(true)
            var alreadyAssigned = ''
            if (
              response.data.alreadyAssigned &&
              response.data.alreadyAssigned.length > 0
            ) {
              angular.forEach(response.data.alreadyAssigned, function (a) {
                alreadyAssigned +=
                  'La tarjeta ' +
                  a.card +
                  ' se encuentra asignada a la unidad ' +
                  a.unit +
                  ' del edificio ' +
                  a.building +
                  '\n'
              })
            }
            alert('Carga finalizada. ' + alreadyAssigned)
          },
          function (response) {
            if (response.status > 0)
              $scope.errorMessage =
                'No se ha podido cargar el archivo. ERROR: ' +
                response.data.result_message
          }
        )
    }

    $scope.refreshBuilding = function () {
      $scope.loadingBuilding = buildingService
        .getBuildingById($scope.building.id)
        .then(function (data) {
          $scope.building = data.data
        })

      $scope.loadBuildingOldestKeepAlive()
    }

    $scope.findPaymentMethod = function (paymentMethod) {
      return $scope.paymentMethods.find((pm) => pm.key === paymentMethod)?.value
    }

    $scope.editClosureType = function () {
      let updatedClosureType = {
        closureType: $scope.building.closureType,
      }

      $scope.loadingBuilding = buildingService
        .editBuildingClosureType($scope.building.id, updatedClosureType)
        .then(function () {
          $scope.building.editClosureType = false
        })
    }

    $scope.saveContact = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editContact = false
        })
    }

    $scope.savePaymentMethod = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editPaymentMethod = false
        })
    }

    $scope.saveBuildingPrepaidRechargableUses = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editBuildingPrepaidRechargableUses = false
        })
    }

    $scope.saveBuildingPreBlockedUses = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editPreBlockedUses = false
        })
    }

    $scope.saveBuildingMaxNumberOfUnits = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editBuildingMaxNumberOfUnits = false
        })
    }

    $scope.saveMP100Parameter = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editMP100 = false
        })
    }

    $scope.saveMP500Parameter = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editMP500 = false
        })
    }

    $scope.saveMP1200Parameter = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.editMP1200 = false
        })
    }

    $scope.editBuildingTime = function () {
      $scope.editingBuildingTime = true
      $scope.building.openingDateTime = $scope.building.openingTime
        ? new Date($scope.building.openingTime)
        : undefined
      $scope.building.closingDateTime = $scope.building.closingTime
        ? new Date($scope.building.closingTime)
        : undefined
    }

    $scope.saveBuildingTime = function () {
      $scope.loadingBuilding = buildingService
        .updateBuilding($scope.building.id, $scope.building)
        .then(function () {
          $scope.building.openingTime =
            $scope.building.openingDateTime?.getTime()
          $scope.building.closingTime =
            $scope.building.closingDateTime?.getTime()
          $scope.editingBuildingTime = false
        })
    }

    $scope.rechargePrepaidUses = function () {
      if (confirm('Esta seguro de recargar los usos para el edificio?')) {
        $scope.loadingBuilding = buildingService
          .rechargePrepaidUses($scope.building.id)
          .then(function () {
            $scope.refreshBuilding()
          })
      }
    }

    $scope.showAssignMachine = function () {
      var modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/machine-selector.html',
        controller: 'MachineSelectorController',
        show: false,
      })

      modal.$promise.then(modal.show)
    }

    $scope.unassignMachineFromBuilding = function (machine) {
      if (confirm('Esta seguro de desasignar la máquina de este edificio?')) {
        $scope.loadingBuilding = buildingService
          .unassignMachine($scope.building.id, machine.id)
          .then(function () {
            $scope.loadMachines(true)
          })
      }
    }

    $scope.assignRPIChild = function (machine) {
      $scope.selectedRPIParent = machine
      const modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/rpi-child-selector.html',
        controller: 'RPIParentController',
        show: false,
      })

      const unsubscribe = $scope.$on('modal.hide', function (modal) {
        if (modal.targetScope.selectedRPIChild?.id) {
          $scope.loadingBuilding = partsService
            .assignRPIChild(
              $scope.selectedRPIParent.id,
              modal.targetScope.selectedRPIChild.id
            )
            .then(function () {
              $scope.loadMachines(true)
            })
        }
        $scope.selectedRPIParent = null
        unsubscribe()
      })

      modal.$promise.then(modal.show)
    }

    $scope.unassignRPIChild = function (machine) {
      if (
        confirm(
          `Esta seguro de desasignar el RPI hijo? (${machine.rpi_child.serial_number})`
        )
      ) {
        $scope.loadingBuilding = partsService
          .unassignRPIChild(machine.id)
          .then(function () {
            $scope.loadMachines(true)
          })
      }
    }

    $scope.zeroingPendingUses = function (machine) {
      if (
        confirm(
          `Esta seguro setear en 0 los usos pendientes de ${machine.serial_number}?`
        )
      ) {
        $scope.loadingBuilding = partsService
          .zeroingPendingUses(machine.id)
          .then(function () {
            $scope.loadMachines(true)
          })
      }
    }

    $scope.isRPIChild = function (machine) {
      const allRPIChilds = $scope.machines
        .filter((machine) => !!machine.rpi_child)
        .map((machine) => machine.rpi_child.id)

      return allRPIChilds.some((id) => id === machine.id)
    }

    $scope.showAssignCard = function (unit) {
      const modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/card-selector.html',
        controller: 'CardSelectorController',
        show: false,
      })

      $scope.unit = unit

      modal.$promise.then(modal.show)
    }

    $scope.unassignCardFromUnit = function (c, u) {
      if (confirm('Esta seguro de desasignar la tarjeta de esta unidad?')) {
        $scope.loadingBuilding = buildingService
          .unassignCard($scope.building.id, u.id, c.uuid)
          .then(function () {
            $scope.loadUnits(true)
          })
      }
    }

    $scope.enableCard = function (c, u) {
      if (confirm('Esta seguro de activar la tarjeta?')) {
        $scope.loadingBuilding = buildingService
          .cardAction($scope.building.id, u.id, c.uuid, 'activate')
          .then(function () {
            $scope.loadUnits(true)
          })
      }
    }

    $scope.disableCard = function (c, u) {
      if (confirm('Esta seguro de desactivar la tarjeta?')) {
        $scope.loadingBuilding = buildingService
          .cardAction($scope.building.id, u.id, c.uuid, 'deactivate')
          .then(function () {
            $scope.loadUnits(true)
          })
      }
    }

    $scope.damagedCard = function (c, u) {
      if (confirm('Esta seguro de marcar la tarjeta como dañada?')) {
        $scope.loadingBuilding = buildingService
          .cardAction($scope.building.id, u.id, c.uuid, 'damaged')
          .then(function () {
            $scope.loadUnits(true)
          })
      }
    }

    $scope.lostCard = function (c, u) {
      if (confirm('Esta seguro de marcar la tarjeta como extraviada?')) {
        $scope.loadingBuilding = buildingService
          .cardAction($scope.building.id, u.id, c.uuid, 'lost')
          .then(function () {
            $scope.loadUnits(true)
          })
      }
    }

    $scope.preBlockCard = function (c, u) {
      if (confirm('Esta seguro de marcar la tarjeta como pre-bloqueada?')) {
        $scope.loadingBuilding = buildingService
          .cardAction($scope.building.id, u.id, c.uuid, 'preBlocked')
          .then(function () {
            $scope.loadUnits(true)
          })
      }
    }

    $scope.getBuildingOldestKeepAlive = function () {
      const machinesWithKeepAlive = building.data.machines.filter(
        (m) => m.last_keep_alive != 'N/D'
      )
      if (!machinesWithKeepAlive.length) {
        $scope.building.lastKeepAlive = 'N/D'
      } else {
        const orderedMachinesWithKeepAlive = machinesWithKeepAlive
          .slice()
          .sort(
            (a, b) =>
              Date.parse(a.last_keep_alive) - Date.parse(b.last_keep_alive)
          )
        $scope.building.lastKeepAlive =
          orderedMachinesWithKeepAlive[0].last_keep_alive
      }
    }

    $scope.loadCardEvents = function (card) {
      $scope.cardEvents = []
      $scope.loadingCardEvents = cardService
        .getCardEvents(card.id)
        .then(function (data) {
          $scope.getCardEventText(card, data.data.card_events)
        })
    }

    $scope.getCardEventText = function (card, cardEvents) {
      const cardCreated = 'CARD_CREATED'
      const cardAssignedBillable = 'CARD_ASSIGNED_BILLABLE'
      const cardAssignedNonBillable = 'CARD_ASSIGNED_NON_BILLABLE'
      const cardUnassigned = 'CARD_UNASSIGNED'
      const cardBlockedNoPayment = 'CARD_BLOCKED_NO_PAYMENT'
      const cardBlockedLost = 'CARD_BLOCKED_LOST'
      const cardBlockedDamaged = 'CARD_BLOCKED_DAMAGED'
      const cardUnblocked = 'CARD_UNBLOCKED'

      $scope.cardEvents.push(card.uuid)

      if (!cardEvents) {
        return
      }

      const orderedCardEvents = cardEvents.sort(
        (a, b) => b.timestamp - a.timestamp
      )

      orderedCardEvents.forEach((cardEvent) => {
        let text = ''
        switch (cardEvent.event_type) {
          case cardCreated:
            text += 'Creación: '
            break
          case cardAssignedBillable:
            text += 'Asignación (billable): '
            break
          case cardAssignedNonBillable:
            text += 'Asignación (no billable): '
            break
          case cardUnassigned:
            text += 'Desasignación: '
            break
          case cardBlockedNoPayment:
            text += 'Bloqueo (falta pago): '
            break
          case cardBlockedLost:
            text += 'Bloqueo (pérdida): '
            break
          case cardBlockedDamaged:
            text += 'Bloqueo (dañada): '
            break
          case cardUnblocked:
            text += 'Desbloqueo: '
            break
          default:
            text += 'Evento no reconocido: '
            break
        }

        text += new Date(cardEvent.timestamp).toLocaleDateString('es-UY')
        $scope.cardEvents.push(text)
      })
    }

    //maintenance-form.html

    $scope.showMaintenanceForm = function () {
      angular.element('.tooltip').remove()
      maintenanceFormTpl.show()
    }

    $scope.hideMaintenanceForm = function () {
      maintenanceFormTpl.hide()
    }

    $scope.editMaintenance = function (item) {
      if (item) {
        item.maintenance_type = $scope.mapMaintenanceTypeCode(
          item.maintenanceType
        )
        item.editing = true
        $scope.item = item
        $scope.settings.cmd = 'Edit'
        $scope.showMaintenanceForm()
      }
    }

    $scope.viewMaintenance = function (item) {
      if (item) {
        item.editing = false
        $scope.item = item
        $scope.settings.cmd = 'View'
        $scope.showMaintenanceForm()
      }
    }

    $scope.createMaintenance = function () {
      $scope.item = {
        editing: true,
      }
      $scope.settings.cmd = 'New'
      $scope.showMaintenanceForm()
    }

    $scope.saveMaintenance = function (item) {
      // to match API expecting format
      const tzoffset = new Date().getTimezoneOffset() * 60000 //offset in milliseconds
      const localISOTime = new Date(
        new Date(item.timestamp) - tzoffset
      ).toISOString()
      item.timestamp = localISOTime

      if ($scope.settings.cmd === 'New') {
        $scope.errorMessage = null
        const maintenance_type = []
        maintenance_type.push(item.maintenance_type)

        const newMaintenance = {
          technician: item.technician,
          maintenance_type,
          timestamp: item.timestamp,
        }

        $scope.savingMaintenance = maintenanceService
          .createMaintenance($scope.building.id, newMaintenance)
          .success(function () {
            $scope.hideMaintenanceForm()
            $scope.loadMaintenances(true)
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido crear el mantenimiento. ' + reason.result_detail
                ? reason.result_detail
                : ''
          })
      } else if ($scope.settings.cmd === 'Edit') {
        $scope.errorMessage = null
        $scope.savingMaintenance = maintenanceService
          .updateMaintenance($scope.building.id, item.id, item)
          .success(function () {
            $scope.hideMaintenanceForm()
            $scope.loadMaintenances(true)
          })
          .error(function (reason) {
            $scope.errorMessage =
              'No se ha podido modificar el mantenimiento. ' +
              reason.result_detail
                ? reason.result_detail
                : ''
          })
      }
    }

    $scope.deleteMaintenance = function (item) {
      if (confirm('Estás seguro?')) {
        maintenanceService
          .deleteMaintenance($scope.building.id, item.id)
          .success(function () {
            $scope.loadMaintenances(true)
          })
      }
    }

    $scope.mapMaintenanceTypeCode = function (maintenanceType) {
      switch (maintenanceType) {
        case 'MP100':
          return 0
        case 'MP500':
          return 1
        case 'MP1200':
          return 2
      }
    }

    $scope.copy = function (event, value) {
      copyService.copy(value).then(function () {
        const element = event.target
        element.classList.add('text-success')
        setTimeout(function () {
          element.classList.remove('text-success')
        }, 2000)
      })
    }

    /* ---------- tiny url ---------- */

    $scope.assignQr = function (machine) {
      $scope.selectedMachine = machine
      const modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/assign-qr-selector.html',
        controller: 'AssignQrController',
        show: false,
      })

      const unsubscribe = $scope.$on('modal.hide', function (modal) {
        if (modal.targetScope.qrToken) {
          $scope.loadingBuilding = tinyUrlService
            .assignTinyUrlToMachine(
              modal.targetScope.qrToken,
              $scope.selectedMachine
            )
            .success(() => {
              $scope.toast.text = 'Asignado con éxito!'
              $scope.toast.type = 'Success'

              $scope.loadMachines(true)
            })
            .error((reason) => {
              $scope.toast.text = `No se ha podido asignar la URL. ${
                reason?.result_detail ?? ''
              }`
              $scope.toast.type = 'Error'
            })
        }
        $scope.selectedMachine = null
        unsubscribe()
      })

      modal.$promise.then(modal.show)
    }

    $scope.unassignQr = function (machine) {
      if (!confirm(`Está seguro desasignar el QR de esta máquina?`)) {
        return
      }

      $scope.loadingBuilding = tinyUrlService
        .unassignTinyUrlFromMachine(machine?.qr?.token, machine)
        .success(() => {
          $scope.toast.text = 'Desasignado con éxito!'
          $scope.toast.type = 'Success'

          $scope.loadMachines(true)
        })
        .error((reason) => {
          $scope.toast.text = `No se ha podido desasignar la URL. ${
            reason?.result_detail ?? ''
          }`
          $scope.toast.type = 'Error'
        })
    }

    $scope.loadBuildingFirstUse = function () {
      buildingService
        .loadBuildingFirstUse($scope.building.id)
        .success(function (data) {
          $scope.building.firstUse = data.firstUse
        })
    }

    $scope.loadBuildingOldestKeepAlive = function () {
      buildingService
        .loadBuildingOldestKeepAlive($scope.building.id)
        .success(function (data) {
          $scope.building.oldestKeepAlive = data.oldestKeepAlive
          $scope.building.oldestKeepAliveFixed = data.oldestKeepAliveFixed
        })
    }

    $scope.showMachineHistory = function (machine) {
      $scope.selectedMachine = machine

      const modal = $modal({
        scope: $scope,
        template: 'assets/tpl/partials/machine-history-record.html',
        controller: 'MachineHistoryController',
        show: false,
      })

      const unsubscribe = $scope.$on('modal.hide', function (modal) {
        $scope.selectedMachine = null
        unsubscribe()
      })

      modal.$promise.then(modal.show)
    }

    $scope.setMachineIntoMaintenance = function (machine) {
      maintenanceService
        .setMachineIntoMaintenance(machine.id)
        .success(function () {
          $scope.toast.text = 'Máquina puesta en mantenimiento'
          $scope.toast.type = 'Success'
          $scope.loadMachines(true)
        })
        .error(function (reason) {
          $scope.toast.text = `No se ha podido poner la máquina en mantenimiento. ${
            reason?.result_detail ?? ''
          }`
          $scope.toast.type = 'Error'
        })
    }

    $scope.interruptMachineMaintenance = function (machine) {
      maintenanceService
        .interruptMachineMaintenance(machine.id)
        .success(function () {
          $scope.toast.text = 'Mantenimiento interrumpido'
          $scope.toast.type = 'Success'
          $scope.loadMachines(true)
        })
        .error(function (reason) {
          $scope.toast.text = `No se ha podido interrumpir el mantenimiento. ${
            reason?.result_detail ?? ''
          }`
          $scope.toast.type = 'Error'
        })
    }

    $scope.refresh = function () {
      $scope.refreshBuilding()
      $scope.loadBuildingFirstUse()
      $scope.loadBuildingOldestKeepAlive()
    }

    $scope.refresh()
  },
])
