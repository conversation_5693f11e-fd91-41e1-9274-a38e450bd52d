app.service('MaintenanceService', [
  '$rootScope',
  'LMAPI',
  'AuthService',
  '$filter',
  '$q',
  function ($rootScope, api, auth, $filter, $q) {
    const self = this
    this.preventiveMaintenance = []
    this.buildingAverageUses = []

    $rootScope.$on('loggedUser:changed', function () {
      self.preventiveMaintenance = []
      self.buildingAverageUses = []
    })

    this.findPreventiveMaintenance = function () {
      return api.findPreventiveMaintenance().then(function (res) {
        self.preventiveMaintenance = res.data.maintenances
        self.buildingAverageUses = res.data.buildingAverageUses
      })
    }

    this.getPreventiveMaintenanceResults = function () {
      return self.preventiveMaintenance.sort()
    }

    this.getBuildingAverageUsesResults = function () {
      return self.buildingAverageUses.sort()
    }

    this.buildTooltip = function (lastMaintenance, uses, type) {
      let text = ''
      if (uses > 0) {
        text = Math.abs(uses) + ' usos'
        if (lastMaintenance) {
          text +=
            ' desde el último mantenimiento realizado el ' + lastMaintenance
        } else {
          text +=
            ' por encima de lo recomendado. Se necesita mantenimiento de tipo ' +
            type
        }
      } else {
        text = 'Faltan ' + Math.abs(uses) + ' para este mantenimiento.'
      }
      return text
    }

    this.createMaintenance = function (buildingId, maintenance) {
      return api.createMaintenance(buildingId, maintenance)
    }

    this.updateMaintenance = function (buildingId, maintenanceId, maintenance) {
      return api.updateMaintenance(buildingId, maintenanceId, maintenance)
    }

    this.deleteMaintenance = function (buildingId, maintenanceId) {
      return api.deleteMaintenance(buildingId, maintenanceId)
    }

    this.invalidateCache = function () {
      return api.invalidateCache()
    }

    this.setMachineIntoMaintenance = function (machineId) {
      return api.setMachineIntoMaintenance(machineId)
    }

    this.interruptMachineMaintenance = function (machineId) {
      return api.interruptMachineMaintenance(machineId)
    }
  },
])
