<div class="dashboard lighten-3" ng-controller="TransactionController">
  <div class="row">
    <div class="col-lg-12">
      <h4 class="p-15 grey-text">Gestión de Transacciones</h4>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div class="panel-heading">Filtros de Búsqueda</div>
        <div class="panel-body">
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"
                ><i class="md md-event-available"></i
              ></span>
              <div class="row">
                <div class="col-md-6">
                  <input
                    bs-datepicker
                    class="form-control"
                    data-max-date="{{untilDate}}"
                    ng-model="filters.from"
                    placeholder="Desde"
                    type="text"
                  />
                </div>
                <div class="col-md-6">
                  <input
                    bs-datepicker
                    class="form-control"
                    data-min-date="{{fromDate}}"
                    ng-model="filters.to"
                    placeholder="Hasta"
                    type="text"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="form-group">
            <div class="input-group">
              <span class="input-group-addon" id="basic-addon1"></span>
              <div class="row">
                <div class="col-md-3">
                  UID
                  <input
                    class="form-control"
                    ng-model="filters.uid"
                    style="width: 100%"
                    type="text"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-footer text-right">
          <button class="btn btn-success btn-xs" ng-click="refresh()">
            Buscar
          </button>
        </div>
      </div>
    </div>
  </div>

  <div class="row-fluid">
    <div class="col-lg-12">
      <div class="panel panel-default">
        <div cg-busy="loadingResults" class="panel-body table-responsive">
          <table class="table">
            <thead>
              <td colspan="9">
                <div class="p-20">
                  <ul class="pager ng-cloak">
                    <div class="btn-group">
                      <button
                        class="btn btn-default"
                        ng-class="{'active': size == pagination.perPage, 'disabled': results && !results.length }"
                        ng-click="setPageSize(size)"
                        ng-repeat="size in pagination.pageSizes"
                        ng-value="size"
                        type="button"
                      >
                        {{size}}
                      </button>
                    </div>
                    <li>
                      <button
                        class="btn btn-default pull-left"
                        ng-class="{'disabled': pagination.page == 1}"
                        ng-click="setPage(pagination.page-1)"
                        type="button"
                      >
                        &laquo; Previo
                      </button>
                    </li>
                    <li>
                      <button
                        class="btn btn-default pull-right"
                        ng-class="{'disabled': results && !results.length}"
                        ng-click="setPage(pagination.page + 1)"
                        type="button"
                      >
                        Siguiente &raquo;
                      </button>
                    </li>
                  </ul>
                </div>
              </td>
            </thead>
            <tr>
              <th ng-click="orderColumn('id')" class="pointer">
                <u>Id</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'id'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th ng-click="orderColumn('amount')" class="pointer">
                <u>Cantidad</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'amount'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>Mensaje de Autorización</th>
              <th ng-click="orderColumn('creationDate')" class="pointer">
                <u>Fecha de Creación</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'creationDate'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>Correo</th>
              <th>Origen</th>
              <th ng-click="orderColumn('uid')" class="pointer">
                <u>UID</u>
                <span
                  class="sortorder"
                  ng-show="filters.orderBy === 'uid'"
                  ng-class="{reverse: filters.reverse}"
                />
              </th>
              <th>Número de CFE</th>
              <th>Edificio/Unidad</th>
              <th>Comisión</th>
            </tr>
            <tr ng-repeat="transaction in results">
              <td>{{transaction.id}}</td>
              <td>{{transaction.amount}}</td>
              <td>{{transaction.authorizationResultMessage}}</td>
              <td>{{transaction.creationDate}}</td>
              <td>{{transaction.email}}</td>
              <td>{{transaction.origin}}</td>
              <td>{{transaction.uid}}</td>
              <td>{{transaction.serie}} - {{transaction.number}}</td>
              <td>{{transaction.recipientName}}</td>
              <td>{{transaction.comission}}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
