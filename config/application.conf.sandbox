# This is the main configuration file for the application.
# ~~~~~

# Secret key
# ~~~~~
# The secret key is used to secure cryptographics functions.
#
# This must be changed for production, but we recommend not changing it in this file.
#
# See http://www.playframework.com/documentation/latest/ApplicationSecret for more details.
play.crypto.secret = "changeme"

# The application languages
# ~~~~~
play.i18n.langs = [ "en", "es" ]

# Router
# ~~~~~
# Define the Router object to use for this application.
# This router will be looked up first when the application is starting up,
# so make sure this is the entry point.
# Furthermore, it's assumed your route file is named properly.
# So for an application router like `my.application.Router`,
# you may need to define a router file `conf/my.application.routes`.
# Default to Routes in the root package (and conf/routes)
# play.http.router = my.application.Routes

# Database configuration
# ~~~~~
# You can declare as many datasources as you want.
# By convention, the default datasource is named `default`
#
db.default.url="********************************************************************************************************************************************************************************************************************"
db.default.username=lavomat
db.default.password="fqAVvYwP*GKws6TrUJ"
db.default.jndiName=DefaultDS

#HikariCP
dbplugin=disabled
db.default.dataSourceClassName=com.mysql.jdbc.jdbc2.optional.MysqlDataSource
db.default.dataSource.user=lavomat
db.default.dataSource.password="fqAVvYwP*GKws6TrUJ"
db.default.dataSource.databaseName=lavomat
db.default.dataSource.serverName=lavomat-sandbox.cq3icpiag9t0.us-west-2.rds.amazonaws.com

db.default.maximumPoolSize=25
db.default.leakDetectionThreshold=15000

jpa.default=defaultPersistenceUnit

# Evolutions
# ~~~~~
# You can disable evolutions if needed
play.evolutions.enabled=false

# You can disable evolutions for a specific datasource if necessary
# play.evolutions.db.default.enabled=false


# Endpoints that don't require security checks
security.exceptions = "[]"

# Long form support
play.http.parser.maxDiskBuffer=100MB
parsers.anyContent.maxLength=100MB
play.http.parser.maxMemoryBuffer=20MB

play.modules.enabled += "playconfig.ConfigModule"

#MQTT AWS BROKER IP
mqtt.broker.ip = "tcp://*************:1883"

## payment getaways ##
payment_getaways.transactions.disabled = false

#MERCADO PAGO SETTINGS - sandbox mercado pagos -
mercadopago.access.token = "APP_USR-6050516613648729-091809-48003d92d2cb3272b921552625c9a2b0-1475918695"
mercadopago.client.id = "6050516613648729"
mercadopago.client.secret = "5SUjFJqMHfkc9F50g4RMikWWMPDwn4yR"

# Bancard settings
bancard.keys.public = "udyqqEGtXHmWlpYruUtq3iSxyFEBwmqW"
bancard.keys.private = "S3qsykbTvjhEW)H.alYLGLmf9e9jSf3bvV+PSYYH"
bancard.webPayment.url = "https://vpos.infonet.com.py:8888"
bancard.keys.secret = "ybA9QZjrtREy2V4EeyKGVBfpaU4d8aUsfRRF"

# Transact settings - Sandbox
transact = {
    lavomat = {
        keys.empcode = "LAVOM1"
        keys.termcode = "T00001"
        keys.emphash = "9A0929EF0430E2B075D8CD6A2422DA75"
        emulacion=true
    }
    lavamar = {
        keys.empcode = "FLYCA1"
        keys.termcode = "T00001"
        keys.emphash = "F25F887003B1CBFA297708359DC9B50F"
        emulacion=true
    }
}

# BambooPayment settings - sandbox
bambooPayment.physicalAgent.keyword = "NMIDGYTSIWW="
bambooPayment.physicalAgent.username = "Test@2022Lavomat"
bambooPayment.physicalAgent.customer.id = "539"
bambooPayment.physicalAgent.customer.key = "CpagahZYH16wigSBviWaykyDqUdX8yno"
bambooPayment.webPayments.customer.key = "CpagahZYH16wigSBviWaykyDqUdX8yno"
bambooPayment.webPayments.customer.id = "539"
bambooPayment.webPayments.url = "https://testing.pagosweb.com.uy/v3.4/requestprocessor.aspx"

play.filters.cors {
  # allow all paths
  pathPrefixes = [
    "/api/",
    "/asst/",
    "/public-site/",
    "/totem/"
  ]
  # allow all origins (You can specify if you want)
  allowedOrigins = [
    "http://public-site-sandbox.lavomat.com.uy",
    "http://totem-sandbox.lavomat.com.uy",
    "http://lavamar-totem-sandbox.lavomat.com.uy",
    "http://assistant-sandbox.lavomat.com.uy",
    "https://testing.pagosweb.com.uy",
    "http://localhost:3000",
  ]
  allowedHttpMethods = ["GET", "POST", "PUT", "OPTIONS", "DELETE"]
  # allow all headers
  allowedHttpHeaders = null
 }

# Environment
api.env = "SANDBOX"
api.baseUrl="https://app-sandbox-ec2.lavomat.com.uy"

# Logger - Papertrail
papertrail.logger.env=sandbox
papertrail.logger.host=logs6.papertrailapp.com
papertrail.logger.port=51618

# Assistant portal
assistant.url="http://assistant-sandbox.lavomat.com.uy"

# Coliving confirmation token
confirmation.token.url="/complete-signup?token="

# Public site base url
publicSite.baseUrl="http://public-site-sandbox.lavomat.com.uy"

# Email
email.smtp.user="<EMAIL>"
email.smtp.password="1Nf0m4t1"
email.smtp.port=465
email.hostname="server107.dinamichosting.com"
email.debug.enabled=true
email.debug.email="<EMAIL>"

email.lavomat.admin="<EMAIL>"
email.lavomat.ceo="<EMAIL>"
email.lavomat.cfo="<EMAIL>"
email.lavomat.asst="<EMAIL>"

# Google reCaptcha v3
google.reCaptcha.secret="6LcrEQskAAAAAGuwJWmuOC2gdFFKWKxfX5tsxrRd"
google.reCaptcha.url="https://www.google.com/recaptcha/api/siteverify"

# Lavomat
lavomat.rut="217231170014"

# Plaza Italia SOAP
plazaItalia.enableNotification=false
plazaItalia.soap.notifySaleUrl=""
plazaItalia.soap.notifyRefundUrl=""
plazaItalia.soap.user=""
plazaItalia.soap.password=""

# Totem
totem.booking.enabled=true

# Redis cache
play.modules.disabled += "play.api.cache.EhCacheModule"
play.modules.enabled += "services.cache.redis.RedisModule"

redis.host="lavomat-cache-wbxqn2.serverless.usw2.cache.amazonaws.com"
redis.port=6379
redis.enabled="true"

# Sicfe
sicfe = {
    lavomat = {
        url="https://testing.cloud.sicfe.uy/Clientes/ERP/Services/sicfe.svc?singleWsdl"
        env="test"
        user="erp_lavoma"
        pass="s5WdFj8VJlTphaYN"
        tenant="lavoma"
        ruc="217231170014"
        companyName="LAVOMAT"
        commercialName="LAVOMAT S.R.L."
        phone="20351108"
        email="<EMAIL>"
    }
    lavamar = {
        url="https://testing.cloud.sicfe.uy/clientes/erp/services/sicfe.svc"
        env="test"
        user="erp_flycar"
        pass="WQfCYcz6FhJjQM3r"
        tenant="flycar"
        ruc="220155820017"
        companyName="LAVAMAR"
        commercialName="FLY CARGAS SAS"
        phone="*********"
        email="<EMAIL>"
    }
}
