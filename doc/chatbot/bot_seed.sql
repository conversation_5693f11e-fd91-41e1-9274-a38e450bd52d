use lavomat_duplicate;

-- presetting

SET SQL_SAFE_UPDATES = 0;


delete from lavomat_duplicate.machine_use where timestamp < '2020-01-01' or (CURDATE() + 1) < timestamp;

update building b inner join (select p.contract_type as ct, bu.building_id as id from building_unit bu inner join unit u on bu.unit_id = u.id inner join unit_assigned_card uac on uac.unit_id = u.id inner join part p on uac.assignedcard_id = p.id group by p.contract_type, bu.building_id) as con on b.id = con.id set b.contract_type = con.ct where b.id >= 0;
update building b set b.contract_type = 'MIXED' where b.id in (59,1069,77,58,1094,1009,1077,105);

update building set address = '18 de Julio 2339', latitude = -34.8977349, longitude = -56.1643574 where id = 12; -- before: Ejido 1029, 0.0, 0.0 ; after: 18 de Julio 2339, -34.8977349, -56.1643574
update building set address = '18 de Julio 2340', latitude = -34.8976352, longitude = -56.164852 where id = 30; -- before: Calle Salto 1293, 0.0, 0.0 ; after: 18 de Julio 2340, -34.8976352, -56.164852
update rate_event set price_card_replacement = 150 where id = 114; -- before: 0; after: 150

update transaction set creation_date = '2021-04-02 12:33:11' where id = 12529; -- before: 2020-11-25 12:21:57; after: 2021-04-02 12:33:11

SET SQL_SAFE_UPDATES = 1;

-- ==== Consulta tarjeta de LAVOMAT  ====

-- ENTRADA	 	Ingresar UID


-- SALIDA 		Su tarjeta no está ingresada en el sistema. 
-- 				Indicar que el 0 es un cero. Pedir ingresar nuevamente. 
-- 				Luego del segundo error con el mismo código derivar a una consulta humana
-- 
-- CARD			0x00000090
select * from lavomat_duplicate.part where uuid = '0x00000090';


-- HITO			SI es PREPAGO, indicamos el saldo actual y a qué edificio pertenece y el apto



-- SALIDA 		Si la carga de saldo es posterior al último keepalive 
-- 				indicamos que vemos que compró $xx saldo a la hora xx:xx, 
-- 				indicamos saldo acrtual y que ese saldo se sincroniza 
-- 				en xx minutos y volver a probar.
-- 
-- CARD			0xc4b617ca
select 
	c.state,	-- ACTIVE
    c.contract_type, 	-- PREPAID
    c.unit_id, 		-- 11295
    b_u.building_id, 	-- 1054
    m.last_alive,	-- 2021-04-02 04:19:03		 
    t.creation_date, -- 2021-04-15 23:24:26
    t.id,	-- 20438
    m.last_alive < t.creation_date as 'desync machine?'		-- 1
    
from part c, unit u, building_unit b_u, building b left join part m on m.building_id = b.id, transaction t
where 1
		and c.uuid = '0xc4b617ca'
        and u.id = c.unit_id
        and b_u.unit_id = u.id
		and b_u.building_id = b.id
		and m.id =	(
						select _m.id
						from part _m
						where 1
							and _m.building_id = b.id
							and ( _m.state = 'ACTIVE' or _m.state = 'NEW' )
						order by _m.last_alive desc
						limit 1
					)
		and t.id =	( 
						select _t.id
                        from transaction _t
                        where 1
							and _t.uid = c.uuid
						order by _t.creation_date desc
                        limit 1
					)
; 


-- SALIDA		Si la carga de saldo es anterior al último keepalive,
-- 				indicamos saldo actual, pedimos desenchufar y volver a enchufar el lavarropas, 
-- 				luego esperar 5 minutos y volver a probar.
-- 
-- CARD			0x946014ca
select 
	c.state,	-- ACTIVE
    c.contract_type, 	-- PREPAID
    c.unit_id, 		-- 13454
    b_u.building_id, 	-- 1087
    m.last_alive,	-- 2021-04-02 04:29:02		 
    t.creation_date, -- 2021-04-02 05:29:02
    t.id,	-- 20432
    m.last_alive < t.creation_date as 'desync machine?'		-- 0
    
from part c, unit u, building_unit b_u, building b left join part m on m.building_id = b.id, transaction t
where 1
		and c.uuid = '0x946014ca'
        and u.id = c.unit_id
        and b_u.unit_id = u.id
		and b_u.building_id = b.id
		and m.id =	(
						select _m.id
						from part _m
						where 1
							and _m.building_id = b.id
							and ( _m.state = 'ACTIVE' or _m.state = 'NEW' )
						order by _m.last_alive desc
						limit 1
					)
		and t.id =	( 
						select _t.id
                        from transaction _t
                        where 1
							and _t.uid = c.uuid
						order by _t.creation_date desc
                        limit 1
					)
; 

-- SALIDA		Si el saldo es inferior a un uso: Se indica el saldo actual y 
-- 				se ofrecen las siguientes opciones
-- 
-- CARD			0x47a069a3
select 
	c.id,	-- 19225
	c.state,	-- ACTIVE
    c.contract_type, 	-- PREPAID
    c.unit_id, 		-- 12441
    b_u.building_id, 	-- 1072
    b.rate_id,	-- 1
    r.name,		-- Tarifa Montevideo sin minimos
    r_e.price_customer,	-- 88
    c.balance,	-- 70
    c.uuid		-- 0x47a069a3
							
from part c, unit u, building_unit b_u, building b, rate r, rate_event r_e
where 1
        and u.id = c.unit_id
        and b_u.unit_id = u.id
        and b_u.building_id = b.id
        and b.rate_id = r.id
        and r_e.id =	( 
							select _r_e.id 
							from rate_event _r_e 
							where 1 
								and _r_e.rate_id = r.id 
								and _r_e.valid_from < curdate() 
								and curdate() < _r_e.valid_until 
								order by _r_e.valid_from desc 
								limit 1
						)
		and c.contract_type = 'PREPAID'
        and c.state = 'ACTIVE'
		and r_e.price_customer > c.balance
        and 0 <	(
					select count(*)
                    from machine_use _m_u
                    where 1
						and c.id = _m_u.card_id
                        and _m_u.timestamp > (curdate() - interval 3 month)
				)
limit 1
;


-- SALIDA		Su tarjeta está bloqueada.
-- 				Desea solicitar la activación de la misma? (02)
--
-- CARD			0xc47352bc
select 
	c.id,	-- 20273
	c.state,	-- INACTIVE
    c.contract_type, 	-- POSTPAID
    c.unit_id, 		-- 532
    b_u.building_id, 	-- 1
    c.balance,	-- 0
    c.uuid		-- 0xc47352bc
							
from part c, unit u, building_unit b_u, building b
where 1
        and u.id = c.unit_id
        and b_u.unit_id = u.id
        and b_u.building_id = b.id
        and c.state = 'INACTIVE'
limit 1
;


-- HITO			La tarjeta es POSTAPGO y esta ok


-- SALIDA		Opción 2: Quiero saber el historial de usos.
--
-- CARD			0x7425be59
select 
	c.id,	-- 351
	c.state,	-- ACTIVE
    c.contract_type, 	-- POSTPAID
    c.unit_id, 		-- 527
    b_u.building_id, 	-- 1
    c.balance,	-- 0
    c.uuid		-- 0x7425be59
							
from part c, unit u, building_unit b_u, building b
where 1
        and u.id = c.unit_id
        and b_u.unit_id = u.id
        and b_u.building_id = b.id
		and c.contract_type = 'POSTPAID'
        and c.state = 'ACTIVE'
        and 0 <	(
					select count(*)
                    from machine_use _m_u
                    where 1
						and c.id = _m_u.card_id
                        and _m_u.timestamp > (curdate() - interval 3 month)
				)
limit 1
;


-- HITO			Opción 1: No puedo activar la máquina
-- ENTRADA		Pedir numero de maquina
-- SALIDA		Pedir desenchufar y volver a enchufar el lavarropas, 
-- 				esperar 5 minutos y volver a probar. Luego dos opciones 
-- 				“FUNCIONA” o “NO FUNCIONA” (03) (Esto lo muestra si es POSTPAGO)
-- 
-- CARD			0x349359bc
-- MACHINE NUM	1
select 
	c.id,	-- 1340
	c.state,	-- ACTIVE
    c.contract_type, 	-- POSTPAID
    c.unit_id, 		-- 1484
    b_u.building_id, 	-- 16
    c.balance,	-- 0
    c.uuid,		-- 0x349359bc
    m.id,	-- 1277
    m.sort_index	-- 1
							
from part c, unit u, building_unit b_u, building b left join part m on m.building_id = b.id
where 1
        and u.id = c.unit_id
        and b_u.unit_id = u.id
        and b_u.building_id = b.id
		and c.contract_type = 'POSTPAID'
        and c.state = 'ACTIVE'
        and m.sort_index > 0
limit 1
;



-- ==== Menu Solicitar tarjeta nueva  ====

-- HITO			Pregunta donde lavar


-- HITO			Opción 1: En mi edificio
-- ENTRADA		Pedir dirección del edificio

-- SALIDA		Si es POSTPAGO. Pedir número de apto. 
-- 				Luego pedir datos, ver especificaciones
--
-- LATITUDE		-34.8977349
-- LONGITUDE	-56.1643574
select
	b.id,	-- 12
    b.name,  -- Estrellas del Sur T12
    b.address,  -- 18 de Julio 2339
	b.city,		-- Montevideo
	b.latitude,		-- -34.897736
    b.longitude,		-- -56.164356
    r.id, 		-- 1
    r_e.price_card_replacement		-- 300

from building b, rate r, rate_event r_e
where 1
	and contract_type = 'POSTPAID'
    and b.city = 'Montevideo'
	and b.id = 12
    and 0 <	(
				select count(*)
				from machine_use _m_u, part _m
				where 1
					and _m_u.timestamp > (curdate() - interval 3 month)
					and _m_u.machine_id = _m.id
					and _m.building_id = b.id
                        
			)
	and b.rate_id = r.id
	and r_e.id =	( 
						select _r_e.id 
						from rate_event _r_e 
						where 1 
							and _r_e.rate_id = r.id 
							and _r_e.valid_from < curdate() 
							and curdate() < _r_e.valid_until 
							order by _r_e.valid_from desc 
							limit 1
					)
;


-- HITO			Si es PREPAGO, indicar costo de tarjeta y preguntar si desea pedirla
--
-- LATITUDE		-34.8977349
-- LONGITUDE	-56.1643574
select
	b.id,	-- 30
    b.name,  -- Sunline
    b.address,  -- 18 de Julio 2340
    b.city,		-- Montevideo
    r.id, 		-- 24
    r_e.price_card_replacement		-- 300
    
from building b, rate r, rate_event r_e
where 1
	and contract_type = 'PREPAID'
	and b.id = 30
    and b.city = 'Montevideo'
    and 0 <	(
					select count(*)
                    from machine_use _m_u, part _m
                    where 1
						and _m_u.timestamp > (curdate() - interval 3 month)
                        and _m_u.machine_id = _m.id
                        and _m.building_id = b.id
                        
				)
	and b.rate_id = r.id
	and r_e.id =	( 
						select _r_e.id 
						from rate_event _r_e 
						where 1 
							and _r_e.rate_id = r.id 
							and _r_e.valid_from < curdate() 
							and curdate() < _r_e.valid_until 
							order by _r_e.valid_from desc 
							limit 1
					)
;


-- HITO			Opción 2: En una lavandería de LAVOMAT

-- SALIDA		Si es PREPAGO, indicar costo de tarjeta y preguntar si desea pedirla
select
	b.id,	-- 1069
    b.name,  -- Lavomat Micenas
    b.address,  -- Av. Brasil 3072 local 21 o Cavia 3087
    b.city,		-- Pocitos
    r.id, 		-- 39
    r.name,		-- Tarifa Local al Publico
    r_e.price_card_replacement		-- 150
    
from building b, rate r, rate_event r_e
where 1
	and building_type = 'LAUNDROMAT'
	and b.rate_id = r.id
	and r_e.id =	( 
						select _r_e.id 
						from rate_event _r_e 
						where 1 
							and _r_e.rate_id = r.id 
							and _r_e.valid_from < curdate() 
							and curdate() < _r_e.valid_until 
							order by _r_e.valid_from desc 
							limit 1
					)
;


-- ==== Ayuda con la APP  ====


-- ENTRADA		Ingresar mail de usuario


-- SALIDA		Su usuario no existe, mostrar pasos para crear ususario.
-- 
-- EMAIL		<EMAIL>
select 
	u.id, -- u.email_address
    u.email_address -- <EMAIL>
from user u
where 1
	and u.email_address = '<EMAIL>'
;


-- SALIDA		El usuario no esta validado. Debe revisar su mail. Nota: ver en SPAM.
-- 
-- EMAIL		<EMAIL>
select 
	u.id, -- 7
    u.email_address, -- <EMAIL>
    a.id,		-- 7
    a.validated		-- 0
from user u, account a
where 1
	and a.owner_id = u.id
    and a.validated = 0
limit 1
;


-- HITO			Si el usuario es PREPAGO Indicamos el saldo actual y a qué edificio pertenece y el apto
-- 
-- NOTA			Un usuario puede estar asociado a mas de una tarjeta
-- 
-- SALIDA		Listado de tarjetas disponibles para el usuario
-- 
-- EMAIL		<EMAIL>
select 
	us.id, -- 353
    us.email_address, -- <EMAIL>
    c.uuid, -- 	0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 0x9acd19aa	ACTIVE, 
			-- 	0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE, 0x34fdebd7	INACTIVE
	c.state,	-- ACTIVE
    c.contract_type, 	-- PREPAID
    c.unit_id, 		-- 4992
    b_u.building_id 	-- 30
from part c, unit u, building_unit b_u, building b left join part m on m.building_id = b.id, transaction t, user us
where 1
		and ( 0 
			or c.prepaidcardholder_id = us.id
            or u.owner_id = us.id
		)
        and u.id = c.unit_id
        and b_u.unit_id = u.id
		and b_u.building_id = b.id
        and us.email_address = '<EMAIL>'
;


-- SALIDA 		Si la carga de saldo es posterior al último keepalive 
-- 				indicamos que vemos que compró $xx saldo a la hora xx:xx, 
-- 				indicamos saldo acrtual y que ese saldo se sincroniza 
-- 				en xx minutos y volver a probar.
-- 
-- EMAIL		<EMAIL>
-- CARD			0xe40feed7
select
	us.id, -- 348
    us.email_address, -- <EMAIL>
	c.state,	-- ACTIVE
    c.contract_type, 	-- PREPAID
    c.unit_id, 		-- 5019
    b_u.building_id, 	-- 30
    m.last_alive,	-- 2021-04-02 04:33:11		 
    t.creation_date, -- 2021-04-02 12:33:11
    t.id,	-- 12529
    m.last_alive < t.creation_date as 'desync machine?'		-- 1
    
from part c, unit u, building_unit b_u, building b left join part m on m.building_id = b.id, transaction t, user us
where 1
		and ( 0 
			or c.prepaidcardholder_id = us.id
            or u.owner_id = us.id
		)
		and c.uuid = '0xe40feed7'
        and u.id = c.unit_id
        and b_u.unit_id = u.id
		and b_u.building_id = b.id
		and m.id =	(
						select _m.id
						from part _m
						where 1
							and _m.building_id = b.id
							and ( _m.state = 'ACTIVE' or _m.state = 'NEW' )
						order by _m.last_alive desc
						limit 1
					)
		and t.id =	( 
						select _t.id
                        from transaction _t
                        where 1
							and _t.uid = c.uuid
						order by _t.creation_date desc
                        limit 1
					)
; 


-- SALIDA		Si la carga de saldo es anterior al último keepalive,
-- 				indicamos saldo actual, pedimos desenchufar y volver a enchufar el lavarropas, 
-- 				luego esperar 5 minutos y volver a probar.
-- 
-- EMAIL		<EMAIL>
-- CARD			0x5468c8d7
select 
	us.id, -- 121
    us.email_address, -- <EMAIL>
	c.state,	-- ACTIVE
    c.contract_type, 	-- PREPAID
    c.unit_id, 		-- 5030
    b_u.building_id, 	-- 30
    m.last_alive,	-- 2021-04-02 04:33:11	 
    t.creation_date, -- 2021-03-27 18:07:43
    t.id,	-- 20036
    m.last_alive < t.creation_date as 'desync machine?'		-- 0
    
from part c, unit u, building_unit b_u, building b left join part m on m.building_id = b.id, transaction t, user us
where 1
		and ( 0 
			or c.prepaidcardholder_id = us.id
            or u.owner_id = us.id
		)
		and c.uuid = '0x5468c8d7'
        and u.id = c.unit_id
        and b_u.unit_id = u.id
		and b_u.building_id = b.id
		and m.id =	(
						select _m.id
						from part _m
						where 1
							and _m.building_id = b.id
							and ( _m.state = 'ACTIVE' or _m.state = 'NEW' )
						order by _m.last_alive desc
						limit 1
					)
		and t.id =	( 
						select _t.id
                        from transaction _t
                        where 1
							and _t.uid = c.uuid
						order by _t.creation_date desc
                        limit 1
					)
; 

-- SALIDA		Si el saldo es inferior a un uso: Se indica el saldo actual y 
-- 				se ofrecen las siguientes opciones
-- 
-- EMAIL		<EMAIL>
-- CARD			0x176862a3
select 
	us.id, -- 369
    us.email_address, --  <EMAIL>
	c.id,	-- 16802
	c.state,	-- ACTIVE
    c.contract_type, 	-- PREPAID
    c.unit_id, 		-- 12459
    b_u.building_id, 	-- 1072
    b.rate_id,	-- 1
    r.name,		-- Tarifa Montevideo sin minimos
    r_e.price_customer,	-- 88
    c.balance,	-- 66
    c.uuid		-- 0x176862a3
    
from part c, unit u, building_unit b_u, building b, rate r, rate_event r_e, user us
where 1
		and ( 0 
			or c.prepaidcardholder_id = us.id
            or u.owner_id = us.id
		)
        and u.id = c.unit_id
        and b_u.unit_id = u.id
        and b_u.building_id = b.id
        and b.rate_id = r.id
        and r_e.id =	( 
							select _r_e.id 
							from rate_event _r_e 
							where 1 
								and _r_e.rate_id = r.id 
								and _r_e.valid_from < curdate() 
								and curdate() < _r_e.valid_until 
								order by _r_e.valid_from desc 
								limit 1
						)
		and c.contract_type = 'PREPAID'
        and c.state = 'ACTIVE'
		and r_e.price_customer > c.balance
        and 0 <	(
					select count(*)
                    from machine_use _m_u
                    where 1
						and c.id = _m_u.card_id
                        and _m_u.timestamp > (curdate() - interval 3 month)
				)
limit 1
;


-- SALIDA		Su tarjeta está bloqueada.
-- 				Desea solicitar la activación de la misma? (02)
--
-- EMAIL		<EMAIL>
-- CARD			0x34fdebd7
select 
	us.id, -- 353
    us.email_address, --  <EMAIL>
	c.id,	-- 4735
	c.state,	-- INACTIVE
    c.contract_type, 	-- POSTPAID
    c.unit_id, 		-- 4992
    b_u.building_id, 	-- 30
    c.balance,	-- 0
    c.uuid		-- 0x34fdebd7
							
from part c, unit u, building_unit b_u, building b, user us
where  1
		and ( 0 
			or c.prepaidcardholder_id = us.id
            or u.owner_id = us.id
		)
        and u.id = c.unit_id
        and b_u.unit_id = u.id
        and b_u.building_id = b.id
        and c.state = 'INACTIVE'
limit 1
;


-- HITO			Opción 1: No puedo activar la máquina
-- ENTRADA		Pedir numero de maquina
-- SALIDA		Pedir desenchufar y volver a enchufar el lavarropas, 
-- 				esperar 5 minutos y volver a probar. Luego dos opciones 
-- 				“FUNCIONA” o “NO FUNCIONA” (03) (Esto lo muestra si es POSTPAGO)
-- 
-- EMAIL		<EMAIL>
-- CARD			0xe48651bc
-- MACHINE NUM	1
select 
	us.id, -- 251
    us.email_address, --  <EMAIL>
	c.id,	-- 1712
	c.state,	-- ACTIVE
    c.contract_type, 	-- POSTPAID
    c.unit_id, 		-- 1897
    b_u.building_id, 	-- 22
    c.balance,	-- 0
    c.uuid,		-- 0xe48651bc
    m.id,	-- 1263
    m.sort_index	-- 1
							
from part c, unit u, building_unit b_u, building b left join part m on m.building_id = b.id, user us
where 1
		and ( 0 
			or c.prepaidcardholder_id = us.id
            or u.owner_id = us.id
		)
        and u.id = c.unit_id
        and b_u.unit_id = u.id
        and b_u.building_id = b.id
		and c.contract_type = 'POSTPAID'
        and c.state = 'ACTIVE'
        and m.sort_index > 0
limit 1
;

