# Chatbots

## WhatsApp

Proveedor: [<PERSON><PERSON><PERSON>](https://dihola.uy/)

### Requerimientos

* [Arbol de decisión en resolución de problemas y fallas](https://docs.google.com/presentation/d/1pFXgwj6U72Koyx9-6Fj6om3eIcj-QJ7Uxk8qQwc1E9Y/edit)

### Documentos útiles

* [Endpoints](https://lmbot.docs.apiary.io/#reference/0/tarjetas/detalle-de-la-tarjeta). Carpeta principal de codigo: [controllers/bot](../../app/controllers/bot/v1/)
* Especificación en formato [API Blueprint](https://apiblueprint.org/):
  * [local](../bot_v1.apib)
  * published on [apiary](https://lmbot.docs.apiary.io/#reference/0/tarjetas/detalle-de-la-tarjeta)
* Documento en [slite](https://lavomat.slite.com/app/channels/4tjNanRfY/notes/Ib60DXNp2)


### Testing

* [Guía de datos de prueba](https://facthory.slite.com/p/note/VfZcyRcp5Kc19jpHxnhr9T). Para ejecutar esta guía es necesario la ejecución previa del script sql [bot_seed.sql](./bot_seed.sql), para la modificación y obtención de los datos. **En caso de realizarse una actualización de datos desde el ambiente de producción, deberia ejecutarse nuevamente este script, ya que algunas consultas dependen de la antiguedad de los datos, como las consutlas de usos o la última vez qeu se recibio un `keep alive`.**
