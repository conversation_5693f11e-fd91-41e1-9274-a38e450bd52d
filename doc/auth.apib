FORMAT: 1A
HOST: https://app-sandbox-ec2.lavomat.com.uy

# LAVOMAT - API para autenticacion

### Sign-in [POST /api/v1/signin]

- Clientes: Many

+ Request

    + Attributes(object)
        + emailAddress: <EMAIL> (string, required) - email del usuario
        + password: bhjsd7dfgn (string, required) - contraseña del usuario

+ Response 200 (application/json)

    + Attributes(object)
        + token: `x1x11111-1x11-11x1-xxx1-1111x1111111` (string) - X-LM-AUTH-TOKEN
        + account(object)
            + user(object)
                + id: 1 (number) - identificador de usuario
                + name: <PERSON> (string) - nombre del usuario
                + lastname: <PERSON> (string) - apellido del usuario
                + email: <EMAIL> (string) - email del usuario
                + role: MASTER (string) - rol de un usuario
                + administration_id: 2 (number) - número identificador de una administración, si es BUILDING_ADM
                + buildingId: 1 (number) - indica el identificador del edificio, si es TOTEM
                + buildingType: LAUNDROMAT (string) - tipo de edificio, si es TOTEM
                + showLastUserInfo: true (boolean) - si puede mostrar la información del último usuario que utlizó la maquina, si es TOTEM

+ Response 400 (application/json)

+ Response 401 (application/json)

+ Response 404 (application/json)


### Sign-up [POST /api/v1/signup]

- Clientes: Mobile app

+ Request

    + Attributes(object)
        + emailAddress: <EMAIL> (string, required) - email del usuario
        + password: bhjsd7dfgn (string, required) - contraseña del usuario
        + phone: 2 2222 2222 (string, required) - telefono del usuario
        + mainAddress: av Brasil (string, required) - dirección del usuario
        + country: Uruguay (string, required) - país en que se encuentra ubicado el edificio
        + name: Juan (string, optional) - nombre del usuario
        + lastname: Perez (string, optional) - apellido del usuario
        + fecha_nacimiento: `2021-04-25` (string, optional) - fecha de nacimientno
        + companyName: POLLOS HERMANOS SA (string, optional) - razón social de la empresa
        + companyRUT: 101010101101 (string, optional) - RUT identificador de la empresa

+ Response 200 (application/json)

    + Attributes(object)
        + token: `x1x11111-1x11-11x1-xxx1-1111x1111111` (string) - X-LM-AUTH-TOKEN
        + emailSent: ERROR (string) - Si no se envio el mail

+ Response 400 (application/json)

+ Response 409 (application/json)

### Sign-out [POST /api/v1/signout{?all-devices}]

- Clientes: Many

+ Parameters
    + all-devices: true (boolean, optional) - indica si se necesita cerrar la sesion en todos los dispositivos

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 204 (application/json)

+ Response 401 (application/json)


### Reset password request [GET /api/v1/resetPassword{?param1}]

- Clientes: Mobile app

+ Parameters
    + param1: <EMAIL> (string, required) - email del usuario

+ Response 200 (application/json)

    + Attributes(object)
        + emailSent: OK (string) - Si se envio el mail. Otros valores: ERROR.

+ Response 400 (application/json)

+ Response 404 (application/json)


### Reset password confirmation [GET /api/v1/confirmResetPassword{?param1}{&param2}]

- Clientes: API FE

+ Parameters
    + param1: 1 (number, required) - identificador de usuario
    + param2: jkdfgj87dfgjn (string, required) - token

+ Response 200 (text/html)


### Validate account [GET /api/v1/validateAccount{?param1}{&param2}]

- Clientes: API FE

+ Parameters
    + param1: 1 (number, required) - identificador de usuario
    + param2: jkdfgj87dfgjn (string, required) - token

+ Response 200 (text/html)


### Account Info [GET /api/v1/account]

- Clientes: BackOffice

+ Request

    + Headers

            X-LM-AUTH-TOKEN: x1x11111-1x11-11x1-xxx1-1111x1111111

+ Response 200 (application/json)

    + Attributes(object)
        + account(object)
            + id: 1 (number) - identificador de la cuenta
            + user(object)
                + id: 1 (number) - identificador de usuario
                + name: Juan (string) - nombre del usuario
                + lastname: Perez (string) - apellido del usuario
                + email: <EMAIL> (string) - email del usuario

+ Response 401 (application/json)
