package serializers;

import global.APIException;
import global.APIException.APIErrors;
import models.BuildingSetting;
import org.json.JSONException;
import org.json.JSONObject;

public class BuildingSettingSerializer {

    public static JSONObject buildingSettingToJson(BuildingSetting buildingSetting)
        throws APIException {
        JSONObject buildingSettingJson = new JSONObject();

        try {
            buildingSettingJson.put(
                "isPreBlockedUseEnabled",
                buildingSetting.isPreBlockedUseEnabled()
            );
            buildingSettingJson.put("preBlockedUses", buildingSetting.getPreBlockedUses());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing building : " + e.getMessage());
        }

        return buildingSettingJson;
    }
}
