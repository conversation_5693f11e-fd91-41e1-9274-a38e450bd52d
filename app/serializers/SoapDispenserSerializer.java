package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.SoapDispenser;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class SoapDispenserSerializer {

    public static JSONObject dispensersListToJson(List<SoapDispenser> dispensers, int level)
        throws APIException {
        JSONObject dispensersJson = new JSONObject();

        JSONArray dispensersListJson = new JSONArray();
        for (SoapDispenser d : dispensers) {
            dispensersListJson.put(dispenserToJson(d, level));
        }

        try {
            dispensersJson.put("dispensers", dispensersListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing card list : " + e.getMessage());
        }
        return dispensersJson;
    }

    public static JSONObject dispenserToJson(SoapDispenser dispenser, int level)
        throws APIException {
        JSONObject dispenserJson = new JSONObject();

        try {
            dispenserJson.put("id", dispenser.getId());
            dispenserJson.put("model", dispenser.getModel());
            dispenserJson.put("description", dispenser.getDescription());
            dispenserJson.put("uses", dispenser.getUses());
            dispenserJson.put("creationDate", dispenser.getCreationDate());
            dispenserJson.put("replenishDate", dispenser.getReplenishDate());
            dispenserJson.put("machineSerial", dispenser.getMachine().getSerialNumber());
            dispenserJson.put("sortIndex", dispenser.getMachine().getSortIndex());
            dispenserJson.put(
                "building",
                dispenser.getMachine().getBuilding() != null
                    ? dispenser.getMachine().getBuilding().getName()
                    : "SIN EDIFICIO"
            );
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing part : " + e.getMessage());
        }

        return dispenserJson;
    }
}
