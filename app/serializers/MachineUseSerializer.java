package serializers;

import domains.sale_notifier.serializers.ExternalSaleNotificationRecordSerializer;
import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.MachineUse;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class MachineUseSerializer {

    public static JSONObject usesListToJson(List<MachineUse> uses, int level) throws APIException {
        JSONObject usesJson = new JSONObject();

        JSONArray usesListJson = new JSONArray();
        for (MachineUse use : uses) {
            usesListJson.put(useToJson(use, level));
        }

        try {
            usesJson.put("uses", usesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine use list : " + e.getMessage());
        }
        return usesJson;
    }

    public static JSONObject useToJson(MachineUse use, int level) throws APIException {
        JSONObject useJson = new JSONObject();

        try {
            useJson.put("id", use.getId());
            useJson.put(
                "machine",
                use.getMachine() != null
                    ? MachineSerializer.machineToJson(use.getMachine(), level)
                    : JSONObject.NULL
            );
            useJson.put(
                "card",
                use.getCard() != null
                    ? CardSerializer.cardToJson(use.getCard(), level)
                    : JSONObject.NULL
            );
            useJson.put("timestamp", use.getTimestamp());
            useJson.put("headline", use.getHeadline());
            useJson.put("accredited", use.isAccredited());
            useJson.put("reason", use.getReason());
            useJson.put("alert", use.isAlert());

            if (level >= 1) {
                useJson.put("channel", use.getChannel());
                useJson.put(
                    "externalNotification",
                    use.getLastExternalSaleNotificationRecord() != null
                        ? ExternalSaleNotificationRecordSerializer.externalSaleNotificationRecordToJSON(
                            use.getLastExternalSaleNotificationRecord()
                        )
                        : JSONObject.NULL
                );
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine use : " + e.getMessage());
        }

        return useJson;
    }
}
