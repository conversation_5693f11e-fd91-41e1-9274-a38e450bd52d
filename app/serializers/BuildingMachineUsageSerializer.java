package serializers;

import dto.report.BuildingMachineUsage;
import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class BuildingMachineUsageSerializer {

    public static JSONObject usesListToJson(List<BuildingMachineUsage> uses) throws APIException {
        JSONObject usesJson = new JSONObject();

        JSONArray usesListJson = new JSONArray();
        for (BuildingMachineUsage u : uses) {
            usesListJson.put(useToJson(u));
        }

        try {
            usesJson.put("uses", usesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine use list : " + e.getMessage());
        }
        return usesJson;
    }

    public static JSONObject useToJson(BuildingMachineUsage u) throws APIException {
        JSONObject useJson = new JSONObject();

        try {
            useJson.put("building_id", u.getBuildingId());
            useJson.put("building_name", u.getBuildingName());
            useJson.put("uses", u.getUses());
            useJson.put("last_maintenance", u.getLastMaintenance());
            useJson.put("last_maintenance_type", u.getMaintenanceType());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine use : " + e.getMessage());
        }

        return useJson;
    }
}
