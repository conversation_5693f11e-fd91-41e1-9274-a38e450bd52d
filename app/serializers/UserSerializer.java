package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.User;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class UserSerializer {

    public static JSONObject userListToJson(List<User> users, int level) throws APIException {
        JSONObject usersJson = new JSONObject();

        JSONArray userListJson = new JSONArray();
        for (User u : users) {
            userListJson.put(userToJson(u, level));
        }

        try {
            usersJson.put("users", userListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing user list : " + e.getMessage());
        }
        return usersJson;
    }

    public static JSONObject userToJson(User user, int level) throws APIException {
        JSONObject userJson = new JSONObject();

        try {
            userJson.put("id", user.getId());
            userJson.put("name", user.getFirstName());
            userJson.put("lastname", user.getLastName());
            userJson.put("email", user.getEmailAddress());
            userJson.put("role", user.getRole());

            if (level >= 1) {
                userJson.put(
                    "buildingId",
                    user.getBuilding() != null ? user.getBuilding().getId() : JSONObject.NULL
                );
                userJson.put(
                    "administrationId",
                    user.getAdministration() != null
                        ? user.getAdministration().getId()
                        : JSONObject.NULL
                );
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing user : " + e.getMessage());
        }

        return userJson;
    }
}
