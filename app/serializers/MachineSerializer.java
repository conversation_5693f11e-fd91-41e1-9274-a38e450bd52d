package serializers;

import domains.back_office.serializers.TinyUrlSerializer;
import global.APIException;
import global.APIException.APIErrors;
import java.util.Date;
import java.util.List;
import models.*;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import services.machine.MachineUsabilityService;

public class MachineSerializer {

    public static JSONObject machineListToJson(List<Machine> machines, int level)
        throws APIException {
        return machineListToJson(machines, level, null);
    }

    public static JSONObject machineListToJson(List<Machine> machines, int level, Date closurDate)
        throws APIException {
        JSONObject machinesJson = new JSONObject();

        JSONArray machinesListJson = new JSONArray();
        for (Machine m : machines) {
            machinesListJson.put(machineToJson(m, level, closurDate));
        }

        try {
            machinesJson.put("machines", machinesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine list : " + e.getMessage());
        }

        return machinesJson;
    }

    public static JSONObject machineToJson(Machine machine, int level) throws APIException {
        return machineToJson(machine, level, null);
    }

    public static JSONObject machineToJson(Machine machine, int level, Date closurDate)
        throws APIException {
        JSONObject machineJson = PartsSerializer.partToJson(machine, level);

        try {
            RateEvent rateEvent = null;
            Rate rate = machine.getMachineRate();
            if (rate != null) {
                rateEvent = rate.getCurrentRateEvent();
                machineJson.put("rateId", rate.getId());
            }

            machineJson.put(
                "priceMachine",
                rateEvent != null ? rateEvent.getPriceCompany() : JSONObject.NULL
            );

            Rate specialRate = machine.getSpecialRate();
            if (specialRate != null) {
                machineJson.put("specialRateId", specialRate.getId());
            }

            machineJson.put(
                "building",
                machine.getBuilding() != null
                    ? BuildingSerializer.buildingToJson(machine.getBuilding(), 1)
                    : JSONObject.NULL
            );
            machineJson.put("machine_type", machine.getMachineType());
            machineJson.put(
                "machine_type_es",
                machine.getMachineType() == Machine.MachineType.WASHER ? "LAVA" : "SECA"
            );
            machineJson.put("capacity", machine.getCapacity());
            machineJson.put("sortIndex", machine.getSortIndex());
            // TODO: remove
            machineJson.put("sort_index", machine.getSortIndex());
            machineJson.put("reference", machine.getReference());

            machineJson.put("english_description", machine.getEnglishDescription());
            machineJson.put("unit_price", machine.getUnitPrice());
            machineJson.put("uy_price", machine.getUyPrice());
            machineJson.put("average_use_time", machine.getAverageUseTime());

            machineJson.put("expected_uses", machine.getExpectedUses());
            machineJson.put("current_uses", machine.getCurrentUses());

            machineJson.put("last_use", JSONObject.NULL);

            MachineUsabilityService service = new MachineUsabilityService(machine);
            MachineUse machineUse = service.getLastUse();
            if (machineUse != null) {
                DateTime lastUse = new DateTime(machineUse.getTimestamp().getTime()).plusHours(3);
                machineJson.put("last_use", lastUse);
                machineJson.put("inMaintenance", service.isInMaintenance());
            }

            machineJson.put(
                "last_unit_use",
                machineUse != null && machineUse.getUnit() != null
                    ? machineUse.getUnit().getNumber()
                    : JSONObject.NULL
            );
            machineJson.put(
                "firmware_version",
                machine.getFirmwareVersion() != null ? machine.getFirmwareVersion() : "Unknown"
            );
            machineJson.put("private_ip", machine.getPrivateIp());
            machineJson.put("public_ip", machine.getPublicIp());
            machineJson.put("port", machine.getPort());

            Date lastKeepAlive = machine.getLastAlive();
            if (lastKeepAlive != null) {
                machineJson.put("last_keep_alive", lastKeepAlive.getTime());
            } else {
                machineJson.put("last_keep_alive", "N/D");
            }

            machineJson.put(
                "lastKeepAlive",
                lastKeepAlive != null ? lastKeepAlive.getTime() : "N/D"
            );

            if (machine.getMachineModel() != null) {
                machineJson.put("machine_model_id", machine.getMachineModel().getId());
                machineJson.put(
                    "machine_model",
                    MachineModelSerializer.machineModelToJson(machine.getMachineModel(), level)
                );
            }

            if (level >= 3) {
                machineJson.put("pending_uses", machine.getPendingUses());

                if (closurDate != null) {
                    machineJson.put(
                        "keep_alive_after_closure_date",
                        machine.isKeepAliveAfterDate(closurDate)
                    );
                    machineJson.put(
                        "uses_after_closure_date",
                        MachineUse.existForMachineAfterDate(machine.getId(), closurDate)
                    );
                }

                if (machine.getRPIChild() != null) {
                    machineJson.put("rpi_child", machineToJson(machine.getRPIChild(), level));
                }

                if (machine.getQr() != null) {
                    machineJson.put("qr", TinyUrlSerializer.itemToJson(machine.getQr(), 1));
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing machine : " + e.getMessage());
        }

        return machineJson;
    }

    public static JSONObject pointOfSaleMachineListToJson(List<Machine> machineList, int level)
        throws APIException {
        JSONObject machinesJson = new JSONObject();
        JSONArray machinesListJson = new JSONArray();

        for (Machine machine : machineList) {
            machinesListJson.put(pointOfSaleMachineToJson(machine));
            // devolvemos ordenada por sort_index
        }

        try {
            machinesJson.put("Machines", machinesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing totem machine list : " + e.getMessage());
        }
        return machinesJson;
    }

    public static JSONObject pointOfSaleMachineToJson(Machine machine) throws APIException {
        JSONObject machineJson = new JSONObject();
        MachineUsabilityService service = new MachineUsabilityService(machine);
        MachineUse lastUse = service.getLastUse();

        try {
            machineJson.put("availability", service.isAvailable());
            machineJson.put("inMaintenance", service.isInMaintenance());
            machineJson.put("machine_id", machine.getId());
            machineJson.put("machine_building", machine.getBuilding().getId());
            machineJson.put("description", machine.getEnglishDescription());
            machineJson.put("machine_type", machine.getMachineType());
            machineJson.put("machine_name", machine.getName());
            machineJson.put("machine_serial", machine.getSerialNumber());
            machineJson.put("machine_rate", machine.getMachineRate().getPriceCustomer(null));
            machineJson.put("machine_remaning_time", service.getRemainingTime());
            machineJson.put(
                "machine_building_is_enable",
                machine.getBuilding().isRemoteActivationEnabled()
            );
            machineJson.put("machine_status_activation", machine.getStatusActivation());
            machineJson.put("machine_sort_index", machine.getSortIndex());
            machineJson.put(
                "machine_show_last_user_info",
                machine.getBuilding().getShowLastUsrInfo()
            );
            machineJson.put("machine_capacity", machine.getCapacity());

            if (machine.getBuilding().getShowLastUsrInfo() && lastUse != null) {
                Card card = lastUse.getCard();

                if (card != null) {
                    Unit unit = card.getUnit();
                    String towerText = unit != null && unit.getTower() != null
                        ? unit.getTower()
                        : "No Disponible";
                    String unitText = unit != null && unit.getNumber() != null
                        ? unit.getNumber()
                        : "No Disponible";

                    machineJson.put(
                        "machine_last_user_info",
                        "Última activación Torre " + towerText + " - Apto " + unitText
                    );
                } else {
                    machineJson.put("machine_last_user_info", "Información no disponible");
                }
            } else {
                machineJson.put("machine_last_user_info", "Información no habilitada");
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing totem machine : " + e.getMessage());
        }

        return machineJson;
    }
}
