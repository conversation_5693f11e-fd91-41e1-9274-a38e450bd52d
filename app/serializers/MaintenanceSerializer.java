package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.*;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class MaintenanceSerializer {

    public static JSONObject maintenanceListToJson(List<Maintenance> uses) throws APIException {
        JSONObject usesJson = new JSONObject();

        JSONArray usesListJson = new JSONArray();
        for (Maintenance u : uses) {
            usesListJson.put(maintenanceToJson(u));
        }

        try {
            usesJson.put("maintenances", usesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing maintenance list : " + e.getMessage());
        }
        return usesJson;
    }

    public static JSONObject maintenanceToJson(Maintenance u) throws APIException {
        JSONObject maintenanceJson = new JSONObject();

        try {
            maintenanceJson.put("id", u.getId());
            maintenanceJson.put("technician", u.getTechnician());
            maintenanceJson.put("timestamp", u.getTimestamp());
            maintenanceJson.put("maintenanceType", u.getMaintenanceType());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing maintenance : " + e.getMessage());
        }

        return maintenanceJson;
    }

    public static JSONObject preventiveMaintenanceEntryWithBuildingAveragesListToJson(
        List<PreventiveMaintenanceBuildingEntry> entries,
        List<BuildingAverageUses> buildingAverageUses
    ) throws APIException {
        JSONObject usesJson = new JSONObject();
        JSONArray entryListJson = new JSONArray();

        for (PreventiveMaintenanceBuildingEntry entry : entries) {
            if (entry.getBuilding().isEnabledForMaintenance()) {
                entryListJson.put(preventiveMaintenanceBuildingEntryToJson(entry));
            }
        }

        JSONArray averagesListJson = new JSONArray();
        for (BuildingAverageUses average : buildingAverageUses) {
            averagesListJson.put(buildingAverageToJson(average));
        }

        try {
            usesJson.put("maintenances", entryListJson);
            usesJson.put("buildingAverageUses", averagesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage(
                    "Error serializing Preventive Maintenance Entry list : " + e.getMessage()
                );
        }
        return usesJson;
    }

    public static JSONObject preventiveMaintenanceEntryWithMachineAveragesListToJson(
        List<PreventiveMaintenanceMachineEntry> entries,
        List<MachineAverageUses> machineAverageUses
    ) throws APIException {
        JSONObject usesJson = new JSONObject();
        JSONArray entryListJson = new JSONArray();

        for (PreventiveMaintenanceMachineEntry entry : entries) {
            entryListJson.put(preventiveMaintenanceMachineEntryToJson(entry));
        }

        JSONArray averagesListJson = new JSONArray();
        for (MachineAverageUses average : machineAverageUses) {
            averagesListJson.put(machineAverageToJson(average));
        }

        try {
            usesJson.put("maintenances", entryListJson);
            usesJson.put("machineAverageUses", averagesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage(
                    "Error serializing Preventive Maintenance Entry list : " + e.getMessage()
                );
        }
        return usesJson;
    }

    public static JSONObject preventiveMaintenanceBuildingEntryToJson(
        PreventiveMaintenanceBuildingEntry entry
    ) throws APIException {
        JSONObject entryJson = new JSONObject();

        try {
            entryJson.put("buildingId", entry.getBuilding().getId());
            entryJson.put("buildingName", entry.getBuilding().getName());
            entryJson.put("department", entry.getBuilding().getDepartment());
            entryJson.put("maintenanceType", entry.getMaintenanceType());
            entryJson.put("uses", entry.getUses());
            entryJson.put("technician", entry.getTechnician());
            entryJson.put("maintenanceDate", entry.getMaintenanceDate());
            entryJson.put(
                "maintenanceParameter",
                maintenanceParameterToJson(entry.getMaintenanceParameter())
            );
            entryJson.put("createdAt", entry.getCreatedAt());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage(
                    "Error serializing Preventive Maintenance Entry : " + e.getMessage()
                );
        }

        return entryJson;
    }

    public static JSONObject preventiveMaintenanceMachineEntryToJson(
        PreventiveMaintenanceMachineEntry entry
    ) throws APIException {
        JSONObject entryJson = new JSONObject();

        try {
            entryJson.put("machineId", entry.getMachine().getId());
            entryJson.put("machineType", entry.getMachine().getMachineType());
            entryJson.put("sort", entry.getMachine().getSortIndex());
            entryJson.put("serialNumber", entry.getMachine().getSerialNumber());
            entryJson.put("model", entry.getModelName());
            entryJson.put("maintenanceType", entry.getMaintenanceType());
            entryJson.put("technician", entry.getTechnician());
            entryJson.put("maintenanceDate", entry.getMaintenanceDate());
            entryJson.put("uses", entry.getUses());
            entryJson.put(
                "maintenanceParameter",
                maintenanceParameterToJson(entry.getMaintenanceParameter())
            );
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage(
                    "Error serializing Preventive Maintenance Entry : " + e.getMessage()
                );
        }

        return entryJson;
    }

    public static JSONObject maintenanceParameterToJson(MaintenanceParameter parameter)
        throws APIException {
        JSONObject entryJson = new JSONObject();

        try {
            entryJson.put("mp100", parameter != null ? parameter.getMp100() : 0);
            entryJson.put("mp500", parameter != null ? parameter.getMp500() : 0);
            entryJson.put("mp1200", parameter != null ? parameter.getMp1200() : 0);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing Maintenance Parameter : " + e.getMessage());
        }

        return entryJson;
    }

    public static JSONObject buildingAverageUsesListToJson(List<BuildingAverageUses> averages)
        throws APIException {
        JSONObject averagesJson = new JSONObject();

        JSONArray averagesListJson = new JSONArray();
        for (BuildingAverageUses average : averages) {
            averagesListJson.put(buildingAverageToJson(average));
        }

        try {
            averagesJson.put("buildingAverages", averagesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage(
                    "Error serializing Building Average Use list : " + e.getMessage()
                );
        }
        return averagesJson;
    }

    public static JSONObject buildingAverageToJson(BuildingAverageUses average)
        throws APIException {
        JSONObject entryJson = new JSONObject();

        try {
            entryJson.put("buildingId", average.getBuildingId());
            entryJson.put("average30Days", average.getAverage30Days());
            entryJson.put("average6Months", average.getAverage6Months());
            entryJson.put("average1Year", average.getAverage1Year());
            entryJson.put(
                "dryerCount",
                average.getDryerCount() != null ? average.getDryerCount() : 0
            );
            entryJson.put(
                "washerCount",
                average.getWasherCount() != null ? average.getWasherCount() : 0
            );
            entryJson.put("createdAt", average.getCreatedAt());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing Building Average Uses : " + e.getMessage());
        }

        return entryJson;
    }

    public static JSONObject machineAverageUsesListToJson(List<MachineAverageUses> averages)
        throws APIException {
        JSONObject averagesJson = new JSONObject();

        JSONArray averagesListJson = new JSONArray();
        for (MachineAverageUses average : averages) {
            averagesListJson.put(machineAverageToJson(average));
        }

        try {
            averagesJson.put("machineAverages", averagesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing Machine Average Use list : " + e.getMessage());
        }
        return averagesJson;
    }

    public static JSONObject machineAverageToJson(MachineAverageUses average) throws APIException {
        JSONObject entryJson = new JSONObject();

        try {
            entryJson.put("machineId", average.getMachineId());
            entryJson.put("average30Days", average.getAverage30Days());
            entryJson.put("average6Months", average.getAverage6Months());
            entryJson.put("average1Year", average.getAverage1Year());
            entryJson.put("totalUses", average.getTotalUses());
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing Machine Average Uses : " + e.getMessage());
        }

        return entryJson;
    }
}
