package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.Part;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

public class PartsSerializer {

    public static JSONObject partListToJson(List<? extends Part> parts, int level)
        throws APIException {
        try {
            JSONObject result = new JSONObject();

            JSONArray partsArray = new JSONArray();

            for (Part part : parts) {
                JSONObject partJSON = partToJson(part, level);
                partsArray.put(partJSON);
            }

            result.put("parts", partsArray);

            return result;
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing administration : " + e.getMessage());
        }
    }

    public static JSONObject partToJson(Part part, int level) throws APIException {
        try {
            JSONObject partJson = new JSONObject();
            partJson.put("id", part.getId());

            if (level >= 1) {
                partJson.put("name", part.getName());
                partJson.put("model", part.getModel());
                partJson.put("description", part.getDescription());
                if (part.getState() != null) {
                    partJson.put("state", part.getState().name());
                }
                partJson.put("serial_number", part.getSerialNumber());
                partJson.put("type", part.getClass().getName());
            }

            return partJson;
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing part : " + e.getMessage());
        }
    }
}
