package serializers;

import global.APIException;
import global.APIException.APIErrors;
import java.util.Date;
import models.Rate;
import org.json.JSONException;
import org.json.JSONObject;

public class RateSerializer {

    public static JSONObject rateToJson(Rate r, int level) throws APIException {
        JSONObject rateJson = new JSONObject();

        try {
            rateJson.put("name", r.getName());
            rateJson.put("id", r.getId());
            rateJson.put("priceCustomer", r.getPriceCustomer(null));
            rateJson.put("priceCompany", r.getPriceCompany(null));
            rateJson.put("priceCardReplacement", r.getPriceCardReplacement(null));

            if (level > 1) {
                rateJson.put("rateType", r.getRateType());
                rateJson.put("appliesIVA", r.appliesIVA());
                rateJson.put("descriptiveMessage", r.getDescriptiveMessage());
                rateJson.put("minUsesPerWasher", r.getMinUsesPerWasher(null));
                rateJson.put("minUsesPerUnit", r.getMinUsesPerUnit(null));
                rateJson.put("priceM3", r.getPriceM3(null));
                rateJson.put("priceKWh", r.getPriceKWh(null));
                Date validFrom = r.getValidFrom(null);
                rateJson.put(
                    "validFrom",
                    validFrom != null ? removeTimeFromDate(validFrom).getTime() : JSONObject.NULL
                );
                Date validUntil = r.getValidUntil(null);
                rateJson.put(
                    "validUntil",
                    validUntil != null ? removeTimeFromDate(validUntil).getTime() : JSONObject.NULL
                );
                Date lastExpiration = r.getLastExpiration();
                rateJson.put(
                    "lastExpiration",
                    lastExpiration != null
                        ? removeTimeFromDate(lastExpiration).getTime()
                        : JSONObject.NULL
                );
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing rate : " + e.getMessage());
        }

        return rateJson;
    }

    private static Date removeTimeFromDate(Date date) {
        date.setHours(0);
        date.setMinutes(0);
        date.setSeconds(0);
        return date;
    }
}
