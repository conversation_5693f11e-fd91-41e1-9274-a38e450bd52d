package controllers.public_site.v1;

import com.play4jpa.jpa.db.Tx;
import global.APIException;
import global.ErrorMessage;
import global.PermissionValidator;
import java.util.ArrayList;
import java.util.List;
import models.*;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import serializers.BuildingSerializer;

@ErrorMessage
@global.LoggingMessage
@Tx
@Security.Authenticated(Secured.class)
public class BuildingController extends BasePublicSiteController {

    /**
     * List coliving buildings which belong to the administration associated to the BUILDING_ADM user
     */
    public Promise<Result> listColivingBuildings() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                Administration administration = getAdministration(ctx());

                List<Building> buildings = new ArrayList<>();
                if (administration != null) {
                    buildings = Building.find(administration.getId(), BuildingType.COLIVING);
                }

                return Promise.pure(
                    ok(BuildingSerializer.buildingListToJson(buildings, queryLevel(0)).toString())
                        .as("application/json")
                );
            },
            Role.BUILDING_ADM
        );
    }
}
