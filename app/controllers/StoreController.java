package controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import domains.billing.exceptions.BillingException;
import domains.payment_gateways.services.trans_act.TransActTransactionFlow;
import global.APIException;
import global.APIException.APIErrors;
import global.ErrorMessage;
import java.io.IOException;
import models.Bill;
import models.Currency;
import models.Machine;
import models.Transaction;
import org.json.JSONException;
import org.json.JSONObject;
import play.libs.F.Promise;
import play.mvc.Result;
import queries.transactions.TransactionQuery;

@ErrorMessage
@global.LoggingMessage
@Tx
public class StoreController extends AbstractController {

    // Para transacciones del totem o kiosko
    // transacciones en cash
    @Deprecated // no se llama del totem!
    @Tx
    public Promise<Result> createTotemTx() throws APIException, IOException, JSONException {
        JsonNode body = request().body().asJson();
        String serial_nro = safeString("serial", body);
        String rut = safeString("rut", body);

        Machine m = Machine.findBySerialNumber(serial_nro);
        if (m == null) throw APIException
            .raise(APIErrors.MACHINE_NOT_FOUND)
            .setDetailMessage("MACHINE_NOT_FOUND");

        Transaction transaction = new Transaction(
            Currency.UYU,
            m.getMachineRate().getPriceCustomer(null),
            null,
            m,
            // este orgien existe?
            // es el origen de los pagos en efectivo?
            // confirmar antes de borrar para dejar registro
            "Totem Payment - LAUNDROMAT - " + m.getBuilding().getId()
        );
        transaction.save();

        if (rut != null && !rut.equals("")) {
            transaction.setRut(rut);
            transaction.update();
        }

        JSONObject response = new JSONObject();
        response.put("Transaction_id", transaction.getPublicId());
        response.put("Transaction_amount", transaction.getAmount());
        response.put("Transaction_currency", transaction.getCurrency());
        return json(response.toString());
    }

    @Deprecated // no se llama del totem!
    public Promise<Result> confirmTotemTx(String id)
        throws APIException, IOException, JSONException {
        Transaction transaction = getTransaction(id);
        transaction.confirm();
        transaction.update();

        try {
            Bill bill = new domains.payment_gateways.services.BaseTransactionFlow()
                // DECLAIMER:
                // all TOTEM transactions needs an associated recipient
                .createBillWithAssociatedRecipient(
                    transaction.getAmount(),
                    transaction.getCurrency(),
                    transaction,
                    transaction.getRecipientBuilding()
                );

            byte[] pdf = new domains.payment_gateways.services.BaseTransactionFlow()
                .generateBillPDF(bill);

            return Promise.<Result>pure(ok(pdf).as("application/pdf"));
        } catch (BillingException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }

        return unreachableReturn();
    }

    /**
     * sends the bill by email
     */
    @Tx
    public Promise<Result> sendTotemTx() throws APIException, IOException, JSONException {
        JsonNode body = request().body().asJson();
        String id = safeString("id", body);
        String email = safeString("email", body);

        Transaction transaction = getTransaction(id);
        transaction.setEmail(email);
        transaction.update();

        Bill bill = Bill.findById(transaction.getBill().getId());

        TransActTransactionFlow service = new TransActTransactionFlow();
        service.sendBillPDF(transaction, bill);

        JSONObject response = new JSONObject();
        response.put("Result:", "OK");
        return json(response.toString());
    }

    private Transaction getTransaction(String publicId) {
        return new TransactionQuery().filterByPublicId(publicId).single();
    }
}
