package controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import domains.activations.ActivationResult;
import domains.activations.MachineActivationService;
import dto.machine.AssignRPIChildParameters;
import dto.machine.UnassignRPIChildParameters;
import dto.machine.ZeroingPendingUsesParameters;
import global.APIException;
import global.APIException.APIErrors;
import global.BackOfficeCop;
import global.ErrorMessage;
import global.PermissionValidator;
import global.PermissionValidator.PromiseCallback;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import models.*;
import models.MachineBooking.MachineBookingStatus;
import models.Part.PartState;
import org.json.JSONObject;
import play.i18n.Messages;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import serializers.MachineSerializer;
import serializers.SoapDispenserSerializer;
import services.building.MachineAvailabilityService;
import services.machine.MachineRPIService;

@ErrorMessage
@global.LoggingMessage(auth = true)
@Security.Authenticated(Secured.class)
public class MachineController extends AbstractController {

    /**
     * Machine activation requested by independent clients,
     * like Mobile app or Totem
     */
    @Tx
    public Promise<Result> activateMachineRequest(
        String uid,
        int buildingId,
        String machineSerial,
        boolean generateMQTTmsj,
        String emailAddress,
        int groupId,
        String expoDeviceToken,
        String channel,
        String trxId
    ) throws APIException {
        User activator = getContextUser();
        ActivationResult result = MachineActivationService
            .instance()
            .usingCard(uid)
            .atBuilding(buildingId)
            .forMachine(machineSerial)
            .sendingMqttMessage(generateMQTTmsj)
            .impersonates(emailAddress)
            .forGroup(groupId)
            .notifyDevice(expoDeviceToken)
            .viaChannel(channel)
            .throughTransaction(trxId)
            .initiatedBy(activator)
            .activate();

        return json(result.getJson());
    }

    /**
     * Machine activation requested by RPIs using physical cards
     */
    @Tx
    public Promise<Result> activateMachineSimpleRequest(
        String uid,
        int buildingId,
        String machineSerial
    ) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                ActivationResult result = MachineActivationService
                    .instance()
                    .usingCard(uid)
                    .atBuilding(buildingId)
                    .forMachine(machineSerial)
                    .viaRPI()
                    .activate();

                return json(result.getJson());
            },
            Role.RPI
        );
    }

    @Tx
    public Promise<Result> bookAMachine() throws APIException {
        JsonNode body = request().body().asJson();

        String emailAddress = body.has("emailAddress") ? body.get("emailAddress").asText() : null;
        String group_id = body.has("group") ? body.get("group").asText() : null;
        String uuid = body.has("uuid") ? body.get("uuid").asText() : null;
        String washers_count = body.has("washers") ? body.get("washers").asText() : null;
        String drying_count = body.has("drying") ? body.get("drying").asText() : null;

        JSONObject response = new JSONObject();

        if (
            emailAddress == null ||
            group_id == null ||
            uuid == null ||
            washers_count == null ||
            drying_count == null
        ) throw APIException.raise(APIErrors.MISSING_PARAMETERS);

        User user = User.findByEmailAddress(emailAddress);
        if (user == null) throw APIException
            .raise(APIErrors.USER_NOT_FOUND)
            .setDetailMessage(Messages.get("user_not_found"));

        WashingGroup group = new WashingGroup();
        if (group_id != "") {
            int groupid = Integer.parseInt(group_id);
            group = WashingGroup.findById(groupid);
            if (group == null) {
                throw APIException
                    .raise(APIErrors.GROUP_NOT_FOUND)
                    .setDetailMessage(Messages.get("GROUP_NOT_FOUND"));
            }
        }

        Card card = Card.findByUID(uuid);

        if (card == null) {
            throw APIException
                .raise(APIErrors.CARD_NOT_FOUND)
                .setDetailMessage(Messages.get("CARD_NOT_FOUND"));
        }

        MachineBooking mb = new MachineBooking();

        if (card.getState() != PartState.ACTIVE) {
            Unit unit = card.getUnit();
            User userUnit = new User();

            if (unit != null) {
                userUnit = unit.getOwner();
            }

            if (userUnit == user) {
                if (unit.isAccredited()) {
                    mb =
                        new MachineBooking(
                            user,
                            group,
                            card,
                            new Date(),
                            washers_count,
                            drying_count,
                            MachineBookingStatus.ACTIVATION_PENDENT
                        );
                    mb.update();
                } else {
                    throw APIException
                        .raise(APIErrors.UNIT_NOT_ACCREDITATED)
                        .setDetailMessage(Messages.get("UNIT_NOT_ACCREDITATED"));
                }
            } else {
                throw APIException
                    .raise(APIErrors.UNIT_INVALID_OWNER)
                    .setDetailMessage(Messages.get("INVALID_OWNER"));
            }
        } else {
            throw APIException
                .raise(APIErrors.INACTIVE_CARD)
                .setDetailMessage(Messages.get("INACTIVE_CARD"));
        }

        try {
            response.put("MachineBooking", mb.getId());
        } catch (Exception e) {
            throw APIException
                .raise(APIErrors.MACHINE_BOOKING_FAILED)
                .setDetailMessage("MACHINE_BOOKING_FAILED");
        }

        return json(response.toString());
    }

    @Tx
    public Promise<Result> deleteMachineBooking(final int machine_book_id) throws APIException {
        JSONObject response = new JSONObject();

        MachineBooking mb = MachineBooking.findById(machine_book_id);

        if (mb == null) {
            throw APIException
                .raise(APIErrors.MACHINE_BOOKING_NOT_FOUND)
                .setDetailMessage(Messages.get("MACHINE_BOOKING_NOT_FOUND"));
        } else {
            // Baja lógica
            mb.setDeletedAt(new Date());
            mb.update();
        }

        try {
            response.put("DeleteMachineBooking", "OK");
        } catch (Exception e) {
            throw APIException
                .raise(APIErrors.DELETE_MACHINE_BOOKING_FAILED)
                .setDetailMessage("DELETE_MACHINE_BOOKING_FAILED");
        }

        return json(response.toString());
    }

    @Tx
    public Promise<Result> getAvailableMachines(final int buildingId) throws APIException {
        MachineAvailabilityService service = new MachineAvailabilityService(
            buildingId,
            getContextUser()
        );
        return json(
            MachineSerializer
                .pointOfSaleMachineListToJson(service.getMachines(), queryLevel(0))
                .toString()
        );
    }

    @Tx
    public Promise<Result> deleteSoapDispenser(final int soapDispId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    SoapDispenser soap_dis = SoapDispenser.findById(soapDispId);

                    if (soap_dis == null) throw APIException.raise(
                        APIErrors.SOAP_DISPENSER_NOT_FOUND
                    );

                    soap_dis.delete();

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.SUPERVISOR
        );
    }

    @Tx
    public Promise<Result> listSoapDispensers() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    List<SoapDispenser> dispensers = new LinkedList<SoapDispenser>();

                    String jsonData = null;

                    dispensers = SoapDispenser.findAll();

                    jsonData =
                        SoapDispenserSerializer.dispensersListToJson(dispensers, 1).toString();

                    return Promise.<Result>pure(ok(jsonData).as("application/json"));
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.ASSISTANT,
            Role.SUPERVISOR
        );
    }

    // Instalation log
    public Promise<Result> createInstallationLog(final int soapDispId) throws APIException {
        JsonNode body = request().body().asJson();

        String machine_id = safeString("machine_id", body);
        String old_building = safeString("old_building", body);
        String new_building = safeString("new_building", body);
        String old_soap_disp = safeString("old_soap_dispenser", body);
        String new_soap_disp = safeString("new_soap_dispenser", body);
        String message = safeString("message", body);

        Machine machine = Machine.findById(Integer.parseInt(machine_id));
        if (machine == null) throw APIException
            .raise(APIErrors.MACHINE_NOT_FOUND)
            .setDetailMessage(Messages.get("MACHINE_NOT_FOUND"));

        SoapDispenser old_soap_dispenser = new SoapDispenser();
        if (old_soap_disp != null) {
            old_soap_dispenser = SoapDispenser.findById(Integer.parseInt(old_soap_disp));
        }

        SoapDispenser new_soap_dispenser = new SoapDispenser();
        if (new_soap_disp != null) {
            new_soap_dispenser = SoapDispenser.findById(Integer.parseInt(new_soap_disp));
        }

        Building old_b = new Building();
        if (old_building != null) {
            old_b = Building.findById(Integer.parseInt(old_building));
        }

        Building new_b = new Building();
        if (new_building != null) {
            new_b = Building.findById(Integer.parseInt(new_building));
        }

        InstallationLog log = new InstallationLog(
            machine,
            new_b,
            old_b,
            new_soap_dispenser,
            old_soap_dispenser,
            message
        );
        log.save();

        return Promise.<Result>pure(created());
    }

    @Tx
    public Promise<Result> assignRPIChild(final int parentId, final int childId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    AssignRPIChildParameters parameters = new AssignRPIChildParameters(
                        childId,
                        parentId
                    )
                        .validate();

                    MachineRPIService machineService = new MachineRPIService();
                    machineService.assignRPIChild(
                        parameters.getRpiParentMachine(),
                        parameters.getRpiChildMachine()
                    );

                    return Promise.<Result>pure(noContent());
                }
            },
            Role.MASTER
        );
    }

    @Tx
    public Promise<Result> unassignRPIChild(final int parentId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    UnassignRPIChildParameters parameters = new UnassignRPIChildParameters(parentId)
                        .validate();

                    MachineRPIService machineService = new MachineRPIService();
                    machineService.unassignRPIChild(parameters.getRpiParentMachine());

                    return Promise.<Result>pure(noContent());
                }
            },
            Role.MASTER
        );
    }

    @Tx
    @BackOfficeCop
    public Promise<Result> zeroingPendingUses(final int machineId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    ZeroingPendingUsesParameters params = new ZeroingPendingUsesParameters(
                        machineId
                    )
                        .validate();

                    Machine machine = params.getMachine();
                    machine.setPendingUses(0);
                    machine.update();

                    return Promise.<Result>pure(noContent());
                }
            },
            Role.MASTER
        );
    }
}
