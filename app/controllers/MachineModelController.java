package controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import global.APIException;
import global.APIException.APIErrors;
import global.ErrorMessage;
import global.PermissionValidator;
import global.PermissionValidator.PromiseCallback;
import java.util.List;
import models.MachineModel;
import models.Role;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import serializers.MachineModelSerializer;

@ErrorMessage
@global.LoggingMessage
@Tx
@Security.Authenticated(Secured.class)
public class MachineModelController extends AbstractController {

    public Result getMachineModels() throws APIException {
        List<MachineModel> machineModels = MachineModel.findAll();
        return ok(
            MachineModelSerializer.machineModelListToJson(machineModels, queryLevel(0)).toString()
        )
            .as("application/json");
    }

    public Promise<Result> createMachineModel() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();
                    dto.machine_model.MachineModelParameters params = new dto.machine_model.MachineModelParameters(
                        body
                    )
                        .validate();

                    MachineModel machineModel = new MachineModel(
                        params.getName(),
                        params.getMaintenanceParameter()
                    );
                    machineModel.save();

                    return Promise.<Result>pure(created());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.ASSISTANT,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> editMachineModel(final int machineModelId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();
                    dto.machine_model.EditMachineModelParameters params = new dto.machine_model.EditMachineModelParameters(
                        body,
                        machineModelId
                    )
                        .validate();

                    MachineModel machineModel = params.getMachineModel();
                    boolean changed = false;
                    if (!machineModel.getName().equals(params.getName())) {
                        machineModel.setName(params.getName());
                        changed = true;
                    }

                    if (!machineModel.getParameter().equals(params.getMaintenanceParameter())) {
                        machineModel.setParameter(params.getMaintenanceParameter());
                        changed = true;
                    }

                    if (changed) {
                        machineModel.update();
                    }

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.ASSISTANT,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> deleteMachineModel(final int machineModelId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    MachineModel machineModel = MachineModel.findById(machineModelId);

                    if (machineModel == null) {
                        throw APIException.raise(APIErrors.MACHINE_MODEL_NOT_FOUND);
                    }

                    machineModel.delete();

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.SUPERVISOR
        );
    }
}
