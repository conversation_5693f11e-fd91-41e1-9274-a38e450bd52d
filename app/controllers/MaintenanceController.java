package controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import dto.maintenance.CreateMaintenanceParameters;
import dto.maintenance.UpdateMaintenanceParameters;
import global.APIException;
import global.APIException.APIErrors;
import global.ErrorMessage;
import global.PermissionValidator;
import global.PermissionValidator.PromiseCallback;
import java.util.List;
import javax.inject.Inject;
import models.*;
import play.libs.F.Promise;
import play.mvc.Result;
import play.mvc.Security;
import security.v1.Secured;
import serializers.MaintenanceSerializer;
import services.MaintenanceService;
import services.cache.CacheService;
import services.cache.redis.RedisService;

@Tx
@ErrorMessage
@global.LoggingMessage
@Security.Authenticated(Secured.class)
public class MaintenanceController extends AbstractController {

    private final CacheService cacheService;

    @Inject
    public MaintenanceController(RedisService redisService) {
        this.cacheService = redisService;
    }

    public Promise<Result> createMaintenance(final int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();
                    CreateMaintenanceParameters dto = new CreateMaintenanceParameters(
                        body,
                        buildingId
                    );

                    MaintenanceService service = new MaintenanceService();
                    service.createMaintenance(dto);

                    return Promise.<Result>pure(created());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.ASSISTANT,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> updateMaintenance(final int buildingId, final int maintenanceId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();
                    UpdateMaintenanceParameters dto = new UpdateMaintenanceParameters(
                        body,
                        buildingId,
                        maintenanceId
                    )
                        .validate();
                    MaintenanceService service = new MaintenanceService();
                    service.updateMaintenance(dto);
                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> deleteMaintenance(final int buildingId, final int maintenanceId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    Building building = Building.findById(buildingId);
                    Maintenance maintenance = Maintenance.findById(maintenanceId);

                    // extra validation
                    if (maintenance.getBuilding().getId() != building.getId()) {
                        throw APIException
                            .raise(APIErrors.BUILDING_NOT_FOUND)
                            .setDetailMessage("BUILDING_NOT_FOUND");
                    }

                    maintenance.delete();
                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> getPreventiveMaintenances() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    String cached = cacheService.cache(
                        "preventive",
                        () -> {
                            MaintenanceService service = new MaintenanceService();
                            List<PreventiveMaintenanceBuildingEntry> entries = service.mapBuildingEntriesWithMaintenanceParameters();
                            List<BuildingAverageUses> buildingAverageUses = service.listBuildingsAverageUses();
                            try {
                                return MaintenanceSerializer
                                    .preventiveMaintenanceEntryWithBuildingAveragesListToJson(
                                        entries,
                                        buildingAverageUses
                                    )
                                    .toString();
                            } catch (APIException e) {
                                throw new RuntimeException(e);
                            }
                        }
                    );

                    return Promise.<Result>pure(ok(cached).as("application/json"));
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> getPreventiveMaintenancesByBuilding(final int buildingId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    return Promise.<Result>pure(ok());
                    //                    MaintenanceService service = new MaintenanceService();
                    //                    List<PreventiveMaintenanceMachineEntry> entries = service.mapMachineEntriesWithMaintenanceParameters(
                    //                        buildingId
                    //                    );
                    //
                    //                    List<MachineAverageUses> machineAverageUses = service.listMachinesAverageUses(
                    //                        buildingId
                    //                    );
                    //                    String jsonEntries = MaintenanceSerializer
                    //                        .preventiveMaintenanceEntryWithMachineAveragesListToJson(
                    //                            entries,
                    //                            machineAverageUses
                    //                        )
                    //                        .toString();
                    //
                    //                    return Promise.<Result>pure(ok(jsonEntries).as("application/json"));
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> getBuildingMaintenances(final int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    List<Maintenance> maintenances = Maintenance.getAll(buildingId);
                    return Promise.<Result>pure(
                        ok(MaintenanceSerializer.maintenanceListToJson(maintenances).toString())
                            .as("application/json")
                    );
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.ASSISTANT,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> assignMaintenanceParameterToBuilding(final int buildingId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();
                    dto.building.AssignMaintenanceParameterToBuildingParameters params = new dto.building.AssignMaintenanceParameterToBuildingParameters(
                        body,
                        buildingId
                    )
                        .validate();

                    MaintenanceService service = new MaintenanceService();
                    service.assignMaintenanceParameterToBuilding(
                        params.getBuilding(),
                        params.getMaintenanceParameter()
                    );

                    return Promise.<Result>pure(created());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> assignMaintenanceParameterToMachineModel(final int machineModelId)
        throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    JsonNode body = request().body().asJson();
                    dto.building.AssignMaintenanceParameterToMachineModelParameters params = new dto.building.AssignMaintenanceParameterToMachineModelParameters(
                        body,
                        machineModelId
                    )
                        .validate();

                    MaintenanceService service = new MaintenanceService();
                    service.assignMaintenanceParameterToMachineModel(
                        params.getMachineModel(),
                        params.getMaintenanceParameter()
                    );

                    return Promise.<Result>pure(created());
                }
            },
            Role.MASTER,
            Role.ADMIN,
            Role.TECHNICIAN,
            Role.SUPERVISOR
        );
    }

    public Promise<Result> setMachineIntoMaintenance(int machineId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    new MaintenanceService().setMachineIntoMaintenance(machineId);

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER
        );
    }

    public Promise<Result> interruptMachineMaintenance(int machineId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    new MaintenanceService().interruptMachineMaintenance(machineId);

                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER
        );
    }

    public Promise<Result> invalidateCache() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            new PromiseCallback() {
                @Override
                public Promise<Result> execute() throws APIException {
                    cacheService.invalidate("preventive");
                    return Promise.<Result>pure(ok());
                }
            },
            Role.MASTER
        );
    }
}
