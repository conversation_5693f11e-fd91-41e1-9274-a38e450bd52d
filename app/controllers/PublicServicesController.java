package controllers;

import com.fasterxml.jackson.databind.JsonNode;
import com.play4jpa.jpa.db.Tx;
import global.APIException;
import global.APIException.APIErrors;
import global.ErrorMessage;
import java.util.Date;
import models.*;
import models.Part.PartState;
import org.apache.commons.mail.EmailException;
import org.json.JSONObject;
import play.libs.F.Promise;
import play.mvc.Result;
import utils.email.EmailService;
import views.html.acceptGroupInvite;
import views.html.errorMessage;
import views.html.generalMessage;

@ErrorMessage
@global.LoggingMessage
public class PublicServicesController extends AbstractController {

    @Tx
    public Result confirmGroupInivtation(String ownerId, String inviteId, String inivteKey)
        throws APIException {
        JSONObject response = new JSONObject();

        if (inivteKey == null || ownerId == null || inviteId == null) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        int user_id = Integer.parseInt(ownerId);
        int invite_id = Integer.parseInt(inviteId);

        User user = User.findById(user_id);
        GroupInvite invite = new GroupInvite();
        invite = invite.findById(invite_id);

        if (invite != null) {
            if (invite.isWaitingForAcceptance()) {
                if (invite.getInviteKey().equals(inivteKey)) {
                    invite.setStatus("ACEPTADA");
                    invite.setAcceptanceDate(new Date());
                    invite.setWaitingForAceptance(false);
                    invite.update();

                    return ok(
                        acceptGroupInvite.render(
                            user.getFirstName(),
                            invite.getGroup().getGroupMainUser().getFirstName(),
                            invite.getGroup().getGroupMainUser().getLastName()
                        )
                    );
                } else {
                    return ok(
                        errorMessage.render(user.getFirstName(), "INVITATION_KEYS_DONT_MATCH")
                    );
                }
            } else {
                return ok(errorMessage.render(user.getFirstName(), "INVITE_ALREADY_ACCEPTED"));
            }
        } else {
            return ok(errorMessage.render(user.getFirstName(), "INVITE_NOT_FOUND"));
        }
    }

    @Tx
    public Result blockUnit(String unit_id, String owner) throws APIException {
        JSONObject response = new JSONObject();

        if (unit_id == null) {
            throw APIException.raise(APIErrors.MISSING_PARAMETERS);
        }

        int uid = Integer.parseInt(unit_id);
        Unit unit = Unit.findById(uid);

        if (unit != null) {
            unit.block();

            for (int i = 0; i < unit.getAssignedCards().size(); i++) {
                Card card = unit.getAssignedCards().get(i);
                card.setState(PartState.INACTIVE);
                card.update();
            }

            unit.update();
            return ok(
                generalMessage.render(
                    "La unidad de diección " + unit.getDireccion() + " fue bloqueada con éxito.",
                    owner
                )
            );
        } else {
            return ok(errorMessage.render(unit.getDireccion(), "UNIT_NOT_FOUND"));
        }
    }

    // mails de la web publica PRUEBA
    @SuppressWarnings("finally")
    @Tx
    public Promise<Result> sendInfoMessage() throws APIException {
        JsonNode body = request().body().asJson();

        String key_msg = body.has("key_msg") ? body.get("key_msg").asText() : null;
        String line1 = body.has("line1") ? body.get("line1").asText() : null;
        String emailAddress = body.has("emailAddress") ? body.get("emailAddress").asText() : null;
        String line2 = body.has("line2") ? body.get("line2").asText() : null;
        String line3 = body.has("line3") ? body.get("line3").asText() : null;
        String bodytxt = body.has("bodytxt") ? body.get("bodytxt").asText() : null;

        JSONObject response = new JSONObject();

        String bodyMsj = "";

        if (key_msg.equals("contact_info")) {
            bodyMsj =
                "El usuario " +
                line1 +
                "(" +
                emailAddress +
                ")" +
                " desea contactarse con Lavomat a traves del siguiente mensaje: " +
                bodytxt;
        } else if (key_msg.equals("contact_faliure")) {
            bodyMsj =
                "Se ha reportado una falla en: " +
                line1 +
                ", el reporte ha llegado a traves de: " +
                emailAddress +
                ", cuyo telefono de contacto es: " +
                line2 +
                ". La falla reportada es: " +
                line3.toUpperCase() +
                ", junto con los siguientes comentarios adicionales: " +
                bodytxt;
        }

        String email = "<EMAIL>";

        try {
            String server = request().host();
            EmailService.send(email, "LAVOMAT  - Contacto recibido desde la web -", bodyMsj);
            response.put("emailSent", "OK");
        } catch (EmailException e) {
            response.put("emailSent", "ERROR");
            e.printStackTrace();
        } finally {
            return json(response.toString());
        }
    }
}
