package controllers;

import com.play4jpa.jpa.db.Tx;
import global.APIException;
import global.ErrorMessage;
import java.util.HashMap;
import java.util.Map;
import models.Building;
import models.Card;
import models.Machine;
import models.User;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import policies.DashboardPolicy;
import policies.actions.Policy;
import security.v1.Secured;
import serializers.HashSerializer;

@ErrorMessage
@global.LoggingMessage
@Security.Authenticated(Secured.class)
@Policy(DashboardPolicy.class)
public class DashboardController extends AbstractController {

    @Tx(readOnly = true)
    public F.Promise<Result> getDashboardData() throws APIException {
        DashboardPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        Map<String, Object> response = new HashMap<>();

        response.put("buildings", Building.count());
        response.put("users", User.count());
        response.put("machines", Machine.count());
        response.put("cards", Card.count());
        // response.put("uses", MachineUse.getStats(7));

        return json(HashSerializer.hashToJson(response));
    }
}
