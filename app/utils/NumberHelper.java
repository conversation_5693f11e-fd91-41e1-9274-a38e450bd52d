package utils;

import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;

public class NumberHelper {

    private NumberHelper() {}

    public static String formatDoubleWithSeparators(Double number) {
        DecimalFormatSymbols dfs = new DecimalFormatSymbols();
        dfs.setDecimalSeparator(',');
        dfs.setGroupingSeparator('.');
        DecimalFormat df = new DecimalFormat("#,##0.00", dfs);
        if (number == null) return "";

        return df.format(number);
    }
}
