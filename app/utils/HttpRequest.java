package utils;

import global.APIException;
import java.io.IOException;
import java.net.URISyntaxException;
import javax.ws.rs.core.HttpHeaders;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response.Status;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpRequestBase;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.json.JSONException;
import org.json.JSONObject;

public class HttpRequest {

    // ================== Request Fields ==================

    private final String uri;
    private String[] headers;
    private String[] params;

    // ================== Response Fields ==================

    private String responseBody;
    private Status status;

    public HttpRequest(String uri) {
        this.uri = uri;
    }

    public HttpRequest headers(String... headers) {
        assert headers.length % 2 == 0;

        this.headers = headers;

        return this;
    }

    public HttpRequest params(String... params) {
        assert params.length % 2 == 0;

        this.params = params;

        return this;
    }

    public HttpRequest post(JSONObject body) throws APIException {
        try {
            HttpPost request = new HttpPost(this.getUrl());

            this.addHeaders(request);

            request.addHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON);
            request.setEntity(new StringEntity(body.toString()));

            this.processResponse(request);
        } catch (Exception e) {
            this.handleRequestFailure(e);
        }

        return this;
    }

    public HttpRequest get() throws APIException {
        try {
            HttpGet request = new HttpGet(this.getUrl());

            this.addHeaders(request);

            this.processResponse(request);
        } catch (Exception e) {
            this.handleRequestFailure(e);
        }

        return this;
    }

    public JSONObject json() {
        try {
            return new JSONObject(this.responseBody);
        } catch (JSONException e) {
            return null;
        }
    }

    public String text() {
        return this.responseBody;
    }

    public boolean succeed() {
        if (this.status == null) {
            throw new RuntimeException("Not executed request!");
        }

        return this.status.getFamily() == Status.Family.SUCCESSFUL;
    }

    public boolean errored() {
        if (this.status == null) {
            throw new RuntimeException("Not executed request!");
        }

        return (
            this.status.getFamily() == Status.Family.CLIENT_ERROR ||
            this.status.getFamily() == Status.Family.SERVER_ERROR
        );
    }

    public boolean redirectioned() {
        if (this.status == null) {
            throw new RuntimeException("Not executed request!");
        }

        return this.status.getFamily() == Status.Family.REDIRECTION;
    }

    public int status() {
        if (this.status == null) {
            throw new RuntimeException("Not executed request!");
        }

        return this.status.getStatusCode();
    }

    private String getUrl() throws URISyntaxException {
        URIBuilder builder = new URIBuilder(this.uri);

        if (this.params != null) {
            for (int i = 0; i < this.params.length; i += 2) {
                builder.addParameter(this.params[i], this.params[i + 1]);
            }
        }

        return builder.toString();
    }

    private void addHeaders(HttpRequestBase request) {
        if (this.headers != null) {
            for (int i = 0; i < this.headers.length; i += 2) {
                request.addHeader(this.headers[i], this.headers[i + 1]);
            }
        }
    }

    private void processResponse(HttpRequestBase request) throws IOException {
        try (
            CloseableHttpClient client = HttpClients.createDefault();
            CloseableHttpResponse response = client.execute(request);
        ) {
            this.status = Status.fromStatusCode(response.getStatusLine().getStatusCode());

            HttpEntity entity = response.getEntity();
            if (entity != null) {
                this.responseBody = EntityUtils.toString(entity);
            }
        }
    }

    private void handleRequestFailure(Exception e) throws APIException {
        throw APIException
            .raise(APIException.APIErrors.INTERNAL_SERVER_ERROR, e)
            .setDetailMessage("Error while trying to make a http request")
            .withParentDetailMessage()
            .withParentStackTrace();
    }
}
