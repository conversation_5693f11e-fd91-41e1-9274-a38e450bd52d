package utils;

import global.APIException;
import global.APIException.APIErrors;
import models.Card;
import models.Part.PartState;
import models.Unit;
import org.apache.commons.lang3.StringUtils;

public class CardHelper {

    public static String sanitizeUid(String uid) {
        if (StringUtils.isBlank(uid)) {
            return "";
        }

        if (
            uid.length() > 1 && uid.length() < 10 && !uid.startsWith("0x") && !uid.startsWith("0X")
        ) return "0x" + uid;

        return uid;
    }

    public static String getUserFriendlyUUID(String uuid) {
        if (uuid == null || uuid.isEmpty()) {
            return "";
        }

        return uuid.replace("0x", "").replace("0X", "");
    }

    @Deprecated // by RechargeableCardValidationService.validate
    public static Card getCardByUid(String uid) throws APIException {
        Card card = null;
        Unit unit = null;
        String price = "";
        String message = "";
        try {
            String sanitizeUid = sanitizeUid(uid);
            if (!sanitizeUid.isEmpty()) {
                card = Card.findByUID(sanitizeUid);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.INTERNAL_SERVER_ERROR, e)
                .withParentDetailMessage()
                .withParentStackTrace();
        }

        if (card == null) throw APIException.raise(APIErrors.CARD_NOT_FOUND);

        if (card.getContractType() == Card.ContractType.POSTPAID) {
            unit = card.getUnit();
            if (unit != null) {
                price = NumberHelper.formatDoubleWithSeparators(unit.getRatePriceCostumer());
                message =
                    "Su tarjeta es POSTPAGO, no requiere precarga. Los usos realizados con su tarjeta se cobran en los gastos comunes.";
                if (price != null) {
                    message += "El costo de cada uso es de $" + price + ".";
                }
                throw APIException.raise(APIErrors.CARD_NOT_PREPAID).setDetailMessage(message);
            }
            throw APIException.raise(APIErrors.CARD_NOT_PREPAID);
        }

        if (card.getState() != PartState.ACTIVE) throw APIException.raise(APIErrors.INACTIVE_CARD);

        return card;
    }
}
