package queries.cards;

import models.Card;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;

public class CardQuery extends queries.PartModelQuery<Card> {

    protected static class Column extends BaseColumn {

        public static final String UUID = "uuid";
    }

    public CardQuery() {
        super(Card.class);
    }

    public CardQuery filterByUid(String uid) {
        if (StringUtils.isNotBlank(uid)) {
            query().getCriteria().add(Restrictions.eq(Column.UUID, uid));
        }

        return this;
    }

    public CardQuery filterByCardHolderPrepaidId(int userId) {
        if (userId > 0) {
            this.query()
                .getCriteria()
                .createAlias("prePaidCardholder", "ppch", JoinType.INNER_JOIN)
                .add(Restrictions.eq("ppch.id", userId));
        }

        return this;
    }
}
