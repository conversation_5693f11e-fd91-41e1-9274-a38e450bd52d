package queries;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * Examples: https://github.com/LAVOMAT/lm-app/pull/152
 *
 * @param <F> finder class from model packages
 * @param <M> model class from model packages
 */
public abstract class BaseFinderModelQuery<F extends com.play4jpa.jpa.models.Model<F>, M> {

    private final Finder<Integer, F> finder;
    private final DefaultQuery<M> query;

    protected abstract static class BaseColumn {

        public static final String ID = "id";
    }

    private BaseFinderModelQuery() {
        throw new UnsupportedOperationException(
            "The child class has to implement a constructor without parameters and call the contractor which receives type Class value (Model.class)."
        );
    }

    protected BaseFinderModelQuery(Class<F> finderClass) {
        finder = new Finder<>(Integer.class, finderClass);
        query = (DefaultQuery<M>) finder.query();
    }

    /*
        Query modifiers
     */

    /**
     * Filter by dates. :warning: Modify dates to the beginning (from) or ending (to) of the date.
     */
    protected <Q extends BaseFinderModelQuery<F, M>> Q filterByDates(
        String column,
        Date from,
        Date to
    ) throws ParseException {
        SimpleDateFormat patternFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (from != null) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
            query.ge(column, patternFormat.parse(format.format(from)));
        }

        if (to != null) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
            query.le(column, patternFormat.parse(format.format(to)));
        }

        return (Q) this;
    }

    protected <Q extends BaseFinderModelQuery<F, M>> Q filterByDate(String column, Date date)
        throws ParseException {
        SimpleDateFormat patternFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (date != null) {
            query.eq(column, patternFormat.parse(patternFormat.format(date)));
        }

        return (Q) this;
    }

    public <Q extends BaseFinderModelQuery<F, M>> Q orderBy(String column) {
        query.orderByAsc(column);

        return (Q) this;
    }

    public <Q extends BaseFinderModelQuery<F, M>> Q orderBy(String column, boolean descendant) {
        if (descendant) {
            query.orderByDesc(column);
        }

        return orderBy(column);
    }

    public <Q extends BaseFinderModelQuery<F, M>> Q filterById(final int id) {
        query().eq(BaseColumn.ID, id);

        return (Q) this;
    }

    /*
        Get final results
        -----------------

        TODO:
            * Contains
            * Min
            * Sum
     */

    public List<M> all() {
        return (List<M>) finder.all();
    }

    public M get(int id) {
        return (M) finder.byId(id);
    }

    public List<M> page(int page, int pageSize) {
        return query.findPage(page, pageSize);
    }

    public List<M> find() {
        return query.findList();
    }

    public M first() {
        return firstOrDefault(null);
    }

    public M firstOrDefault(M defaultValue) {
        List<M> list = query.orderByAsc(BaseColumn.ID).setMaxRows(1).findList();
        if (list.isEmpty()) {
            return defaultValue;
        } else {
            return list.get(0);
        }
    }

    public M last() {
        return lastOrDefault(null);
    }

    public M lastOrDefault(M defaultValue) {
        List<M> list = query.orderByDesc(BaseColumn.ID).setMaxRows(1).findList();
        if (list.isEmpty()) {
            return defaultValue;
        } else {
            return list.get(0);
        }
    }

    /**
     * Convenience method to return a single instance that matches
     * the query, or null if the query returns no results.
     *
     * @return the single result or null
     * @throws javax.persistence.NonUniqueResultException if there is more than one matching result
     * @see org.hibernate.Criteria#uniqueResult()
     */
    public M single() {
        return query.findUnique();
    }

    public M singleLocked() {
        M entity = single();
        javax.persistence.EntityManager entityManager = play.db.jpa.JPA.em();
        entityManager.lock(entity, javax.persistence.LockModeType.PESSIMISTIC_WRITE);
        return entity;
    }

    public long count() {
        return query.findRowCount();
    }

    public boolean any() {
        return query.setMaxRows(1).findRowCount() > 0;
    }

    public List<M> take(int max) {
        return query.setMaxRows(max).findList();
    }

    public int max(String column) {
        return query.findMaxValue(column);
    }

    /*
        Internal methods
     */

    protected DefaultQuery<M> query() {
        return query;
    }
}
