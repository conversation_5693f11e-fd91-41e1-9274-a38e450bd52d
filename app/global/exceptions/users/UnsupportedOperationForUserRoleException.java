package global.exceptions.users;

import global.APIException;
import models.Role;
import play.i18n.Messages;
import play.mvc.Http;

public class UnsupportedOperationForUserRoleException extends APIException {

    public UnsupportedOperationForUserRoleException() {
        super(Messages.get("UNSUPPORTED_OPERATION_FOR_USER_ROLE"), Http.Status.FORBIDDEN);
    }

    public APIException setDetailMessage(Role role) {
        return super.setDetailMessage("User's role " + role.toString() + " is not expected.");
    }
}
