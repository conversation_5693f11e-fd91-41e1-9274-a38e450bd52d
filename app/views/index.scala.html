@(message: String) @main(message) {
    <!-- headers tag -->

    <link rel="stylesheet" href="assets/css/app.css">
    <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/trix/0.9.2/trix.css">
} {
        <!-- content tag -->

    <body
    id="top"
    ng-class="[theme.template, theme.color]"
    ng-controller="MainController"
    scroll-spy
    >
        <main
        ng-class="{'side-nav':(page.hideSidebar == undefined || page.hideSidebar == false)}"
        style="height: 100%"
        >
            <div
            ng-if="page.hideSidebar == undefined || page.hideSidebar == false"
            ng-include
            src="'/assets/tpl/partials/sidebar.html'"
            ></div>
            <div class="main-container" style="height: 100%">
                <div
                ng-if="page.hideTopbar == undefined || page.hideTopbar == false"
                ng-include
                src="'/assets/tpl/partials/topnav.html'"
                ></div>
                <div
                autoscroll="true"
                bs-affix-target
                class="main-content"
                init-ripples
                ng-class="{'top-nav':(page.hideTopbar == undefined || page.hideTopbar == false)}"
                ng-cloak
                ng-view
                style="height: 100%"
                ></div>
            </div>
        </main>

        <div class="alert-container-top-right"></div>
            <!--
    <script src="/public/dist/assets/js/vendors.min.js"></script>
    <script src="/public/dist/assets/js/app.min.js"></script>
    -->

        <script src="//cdnjs.cloudflare.com/ajax/libs/trix/0.9.2/trix.js"></script>

        <script
        charset="utf-8"
        src="public/bower_components/jquery/dist/jquery.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/bootstrap-sass/assets/javascripts/bootstrap.js"
        ></script>

        <script
        charset="utf-8"
        src="public/bower_components/angular/angular.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-route/angular-route.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-animate/angular-animate.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-chroma/angular-chroma.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-ui-select/dist/select.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-sanitize/angular-sanitize.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-local-storage/dist/angular-local-storage.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-loading-bar/build/loading-bar.js"
        ></script>

        <script
        charset="utf-8"
        src="public/bower_components/ng-table/dist/ng-table.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-auto-validate/dist/jcs-auto-validate.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-strap/dist/angular-strap.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-strap/dist/angular-strap.tpl.js"
        ></script>

            <!-- shim is needed to support non-HTML5 FormData browsers (IE8-9)-->
        <script
        charset="utf-8"
        src="assets/js/vendors/ng-file-upload-file-api.js"
        ></script>

        <script
        charset="utf-8"
        src="public/bower_components/hammerjs/hammer.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/jquery-hammerjs/jquery.hammer.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/velocity/velocity.js"
        ></script>

        <script charset="utf-8" src="public/bower_components/d3/d3.js"></script>
        <script charset="utf-8" src="public/bower_components/c3/c3.js"></script>
        <script
        charset="utf-8"
        src="public/bower_components/c3-angular/c3js-directive.js"
        ></script>

        <script
        charset="utf-8"
        src="public/bower_components/lodash/dist/lodash.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-google-maps/dist/angular-google-maps.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/bower-jvectormap/jquery-jvectormap-1.2.2.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/bower-jvectormap/jquery-jvectormap-world-mill-en.js"
        ></script>

        <script
        charset="utf-8"
        src="public/bower_components/nouislider/distribute/jquery.nouislider.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-elastic/elastic.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/ng-file-upload/ng-file-upload-shim.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/ng-file-upload/ng-file-upload.js"
        ></script>

        <script
        charset="utf-8"
        src="public/bower_components/textAngular/dist/textAngular-rangy.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/textAngular/dist/textAngular-sanitize.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/textAngular/dist/textAngular.min.js"
        ></script>

        <script
        charset="utf-8"
        src="public/bower_components/angulartics/dist/angulartics.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angulartics/dist/angulartics-ga.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/angular-busy/dist/angular-busy.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/ng-accordion/ng-accordion/dist/ng-accordion.min.js"
        ></script>
        <script
        charset="utf-8"
        src="public/bower_components/ng-file-upload/ng-file-upload.min.js"
        ></script>
            <!-- endbuild -->
            <!-- build:js assets/js/app.min.js -->
        <script
        charset="utf-8"
        src="assets/js/vendors/angular-placeholders.js"
        ></script>
        <script charset="utf-8" src="assets/js/vendors/side-nav.js"></script>
        <script charset="utf-8" src="assets/js/vendors/ripples.js"></script>
        <script
        charset="utf-8"
        src="assets/js/vendors/fsm-sticky-header.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/vendors/angular-smooth-scroll.js"
        ></script>

        <script src="public/bower_components/angular-trix/dist/angular-trix.min.js"></script>
        <script src="public/bower_components/select2/dist/js/select2.js"></script>
        <script src="public/bower_components/angularjs-dropdown-multiselect/dist/angularjs-dropdown-multiselect.min.js"></script>
        <script charset="utf-8" src="assets/js/colors.js"></script>

        <script charset="utf-8" src="assets/js/app.js"></script>
        <script charset="utf-8" src="assets/js/app.constants.js"></script>
        <script charset="utf-8" src="assets/js/app.config.js"></script>
        <script charset="utf-8" src="assets/js/app.filters.js"></script>
        <script charset="utf-8" src="assets/js/app.demo.js"></script>
        <script charset="utf-8" src="assets/js/components/ngListSelect.js"></script>
        <script charset="utf-8" src="assets/js/directives/formcontrol.js"></script>
        <script charset="utf-8" src="assets/js/directives/navbar-hover.js"></script>
        <script
        charset="utf-8"
        src="assets/js/directives/navbar-search.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/directives/navbar-toggle.js"
        ></script>
        <script charset="utf-8" src="assets/js/directives/noui-slider.js"></script>
        <script charset="utf-8" src="assets/js/directives/todo-widget.js"></script>
        <script charset="utf-8" src="assets/js/directives/menu-link.js"></script>
        <script charset="utf-8" src="assets/js/directives/menu-toggle.js"></script>
        <script charset="utf-8" src="assets/js/directives/vectormap.js"></script>
        <script charset="utf-8" src="assets/js/directives/autofocus.js"></script>
        <script charset="utf-8" src="assets/js/directives/card-flip.js"></script>
        <script charset="utf-8" src="assets/js/directives/scroll-spy.js"></script>
        <script charset="utf-8" src="assets/js/directives/init-ripples.js"></script>
        <script charset="utf-8" src="assets/js/directives/disable-arrows.js"></script>
        <script
        charset="utf-8"
        src="assets/js/directives/lm-directives.js"
        ></script>
        <script charset="utf-8" src="assets/js/directives/toast.js"></script>

        <script charset="utf-8" src="assets/js/services/color-service.js"></script>
        <script charset="utf-8" src="assets/js/services/todo-service.js"></script>
        <script charset="utf-8" src="assets/js/services/auth.js"></script>
        <script charset="utf-8" src="assets/js/services/api.js"></script>
        <script charset="utf-8" src="assets/js/services/account.js"></script>
        <script charset="utf-8" src="assets/js/services/administration.js"></script>
        <script charset="utf-8" src="assets/js/services/user.js"></script>
        <script charset="utf-8" src="assets/js/services/building.js"></script>
        <script charset="utf-8" src="assets/js/services/parts-service.js"></script>
        <script charset="utf-8" src="assets/js/services/machine.js"></script>
        <script charset="utf-8" src="assets/js/services/report.js"></script>
        <script charset="utf-8" src="assets/js/services/billing.js"></script>
        <script charset="utf-8" src="assets/js/services/maintenance.js"></script>
        <script charset="utf-8" src="assets/js/services/maintenance-detail.js"></script>
        <script charset="utf-8" src="assets/js/services/branding.js"></script>
        <script charset="utf-8" src="assets/js/services/machine-model.js"></script>
        <script charset="utf-8" src="assets/js/services/audit.js"></script>
        <script charset="utf-8" src="assets/js/services/transaction.js"></script>
        <script charset="utf-8" src="assets/js/services/utilities.js"></script>
        <script charset="utf-8" src="assets/js/services/copy.js"></script>
        <script charset="utf-8" src="assets/js/services/tiny-url.js"></script>
        <script charset="utf-8" src="assets/js/services/rate.js"></script>
        <script charset="utf-8" src="assets/js/services/card.js"></script>
        <script charset="utf-8" src="assets/js/services/totems.js"></script>

        <script charset="utf-8" src="assets/js/controllers/main.js"></script>
        <script charset="utf-8" src="assets/js/controllers/login.js"></script>
        <script charset="utf-8" src="assets/js/controllers/sidebar.js"></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/administrations.js"
        ></script>
        <script charset="utf-8" src="assets/js/controllers/users.js"></script>
        <script charset="utf-8" src="assets/js/controllers/buildings.js"></script>
        <script charset="utf-8" src="assets/js/controllers/rates.js"></script>
        <script charset="utf-8" src="assets/js/controllers/parts.js"></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/building-detail.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/common/modals.js"
        ></script>
        <script charset="utf-8" src="assets/js/controllers/reports.js"></script>
        <script charset="utf-8" src="assets/js/controllers/uses-report.js"></script>
        <script charset="utf-8" src="assets/js/controllers/billing.js"></script>
        <script charset="utf-8" src="assets/js/controllers/maintenance.js"></script>
        <script charset="utf-8" src="assets/js/controllers/maintenance-detail.js"></script>
        <script charset="utf-8" src="assets/js/controllers/branding.js"></script>
        <script charset="utf-8" src="assets/js/controllers/machine-model.js"></script>
        <script charset="utf-8" src="assets/js/controllers/audit.js"></script>
        <script charset="utf-8" src="assets/js/controllers/transaction.js"></script>
        <script charset="utf-8" src="assets/js/controllers/utilities.js"></script>
        <script charset="utf-8" src="assets/js/controllers/tiny-url.js"></script>
        <script charset="utf-8" src="assets/js/controllers/totemMachineMappings.js"></script>
        <script charset="utf-8" src="assets/js/controllers/totemPosTerminalMappings.js"></script>

        <script charset="utf-8" src="assets/js/controllers/dashboard.js"></script>
        <script charset="utf-8" src="assets/js/controllers/charts.js"></script>
        <script charset="utf-8" src="assets/js/controllers/colors.js"></script>
        <script charset="utf-8" src="assets/js/controllers/buttons.js"></script>
        <script charset="utf-8" src="assets/js/controllers/lists.js"></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/maps/full-map.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/maps/basic-map.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/maps/clickable-map.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/maps/searchable-map.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/maps/zoomable-map.js"
        ></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/maps/vector-map.js"
        ></script>
        <script charset="utf-8" src="assets/js/controllers/forms.js"></script>
        <script charset="utf-8" src="assets/js/controllers/upload.js"></script>
        <script
        charset="utf-8"
        src="assets/js/controllers/tables/basic.js"
        ></script>
        <script charset="utf-8" src="assets/js/controllers/apps/crud.js"></script>
        <script charset="utf-8" src="assets/js/controllers/apps/todo.js"></script>
    </body>

}
