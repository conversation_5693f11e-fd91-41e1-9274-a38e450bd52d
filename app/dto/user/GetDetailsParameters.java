package dto.user;

import global.APIException;
import global.APIException.*;
import models.Account;
import models.User;

public class GetDetailsParameters extends dto.ActionParameters {

    protected String email;
    protected User user;

    public GetDetailsParameters() {}

    public GetDetailsParameters(String email) throws APIException {
        this.email = email;
    }

    public GetDetailsParameters validate() throws APIException {
        if (email == null || email.isEmpty()) throw APIException.raise(
            APIErrors.MISSING_PARAMETERS
        );

        this.user = User.findByEmailAddress(email);

        if (user == null) throw APIException.raise(APIErrors.USER_NOT_FOUND);

        Account account = user.getMasterAccount();
        if (
            account != null && (account.getValidated() == null || !account.getValidated())
        ) throw APIException.raise(APIErrors.USER_NOT_CONFIRMED);

        return this;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
