package dto.building;

import com.fasterxml.jackson.databind.JsonNode;
import dto.maintenance.MaintenanceParameterParameters;
import global.APIException;
import global.APIException.*;
import models.MachineModel;

public class AssignMaintenanceParameterToMachineModelParameters
    extends MaintenanceParameterParameters {

    protected MachineModel machineModel;

    public AssignMaintenanceParameterToMachineModelParameters(JsonNode body, int machineModelId)
        throws APIException {
        super(body);
        machineModel = MachineModel.findById(machineModelId);
    }

    public AssignMaintenanceParameterToMachineModelParameters validate() throws APIException {
        super.validate();

        if (machineModel == null) throw APIException.raise(APIErrors.MACHINE_MODEL_NOT_FOUND);

        return this;
    }

    public MachineModel getMachineModel() {
        return this.machineModel;
    }
}
