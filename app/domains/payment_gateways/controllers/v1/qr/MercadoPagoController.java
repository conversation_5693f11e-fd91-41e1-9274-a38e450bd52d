package domains.payment_gateways.controllers.v1.qr;

import com.mercadopago.MercadoPago;
import com.mercadopago.resources.Payment;
import com.play4jpa.jpa.db.Tx;
import domains.activations.Channel;
import domains.activations.MachineActivationService;
import domains.payment_gateways.controllers.v1.PaymentGatewaysBaseController;
import domains.payment_gateways.services.mercado_pago.MercadoPagoTransactionFlow;
import global.APIException;
import global.exceptions.PosNotFoundException;
import global.exceptions.StoreNotFoundException;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import models.*;
import org.json.JSONException;
import org.json.JSONObject;
import play.Play;
import play.db.jpa.JPA;
import play.i18n.Messages;
import play.libs.F;
import play.mvc.Result;
import queries.stores.PoSQuery;
import utils.ApplicationConfiguration;

public class MercadoPagoController extends PaymentGatewaysBaseController {

    /**
     * GET /api/v1/qr/order
     * called by MP when users scan the QR.
     * 1. validate if the machine is available to be used.
     * 2. create Transaction record
     * 3. return the order object which contains payment info (200)
     */
    @Tx
    public F.Promise<Result> create(String ref) throws APIException, IOException, JSONException {
        play.Logger.debug("## QR FLOW ## createPOSorder ## machine_serial: {}", ref);
        play.Logger.debug("CREATE ORDER REF>>> " + ref);

        if (ApplicationConfiguration.arePaymentGatewaysTransactionsDisabled()) {
            play.Logger.debug("Service disabled>>> ");
            play.Logger.debug(
                "## QR FLOW ## createPOSorder ## Service disabled ## machine_serial: {}",
                ref
            );

            JSONObject error = new JSONObject();
            error.put("type", "in_process");
            error.put("message", Messages.get("unavailable_operation_ex"));
            JSONObject response = new JSONObject();
            response.put("error", error);

            String json = response.toString();
            play.Logger.debug("ENVIANDO ERROR MP>>> " + json);
            return F.Promise.pure(play.mvc.Results.badRequest(json).as("application/json"));
        }

        Machine machine = Machine.findBySerialNumber(ref);
        if (machine == null) {
            play.Logger.error(
                "## QR FLOW ## createPOSorder ## Machine not found - machine_serial: {}",
                ref
            );

            throw APIException
                .raise(APIException.APIErrors.MACHINE_NOT_FOUND)
                .setDetailMessage("MACHINE_NOT_FOUND");
        }

        if (
            !MachineActivationService
                .instance()
                .canActivateMachineByAnonymous(machine, Channel.QR_CHANNEL)
        ) {
            play.Logger.error(
                "## QR FLOW ## createPOSorder ## Machine is NOT available - machine_serial: {}",
                ref
            );

            JSONObject jsonInputErr = new JSONObject();
            JSONObject jsonItemErr = new JSONObject();
            jsonItemErr.put("type", "in_process");
            jsonItemErr.put("message", "Máquina en uso, espere por favor y reintente más tarde.");
            jsonInputErr.put("error", jsonItemErr);

            String jsonInputStringErr = jsonInputErr.toString();

            play.Logger.debug("ENVIANDO ERROR MP>>> " + jsonInputStringErr);

            // return json(jsonInputStringErr);
            return F.Promise.<Result>pure(
                play.mvc.Results.badRequest(jsonInputStringErr).as("application/json")
            );
        }
        play.Logger.debug(
            "## QR FLOW ## createPOSorder ## Machine IS available - machine_serial: {}",
            ref
        );

        POS pos = new PoSQuery().filterByMachineSerialNumber(ref).single();
        if (pos == null) {
            play.Logger.error(
                "## QR FLOW ## createPOSorder ## POS not found - machine_serial: {}",
                ref
            );
            throw new PosNotFoundException();
        }

        Store store = pos.getStore();
        if (store == null) {
            play.Logger.error(
                "## QR FLOW ## createPOSorder ## Store not found - machine_serial: {}",
                ref
            );
            throw new StoreNotFoundException();
        }

        String server = request().host();

        String collector_id = Play
            .application()
            .configuration()
            .getString("mercadopago.access.token")
            .split("-")[4];

        JSONObject jsonInput = new JSONObject();
        jsonInput.put("collector_id", collector_id);

        ArrayList<JSONObject> items = qrOrderItemsSerializer(machine);
        jsonInput.put("items", items);

        Transaction transaction = createQROrderTransaction(machine);
        play.Logger.debug(
            "## QR FLOW ## createPOSorder ## created transaction - machine_serial: {} - transaction.id: {}",
            ref,
            transaction.getId()
        );

        jsonInput.put("external_reference", ref + "_" + transaction.getPublicId());
        jsonInput.put("notification_url", "https://" + server + "/api/v1/qr/notify");

        String jsonInputString = jsonInput.toString();

        play.Logger.debug("CREATE ORDER>>> " + jsonInputString);
        play.Logger.debug(
            "## QR FLOW ## createPOSorder ## OK - machine_serial: {} - response: {}",
            ref,
            jsonInputString
        );

        return json(jsonInputString);
    }

    private ArrayList<JSONObject> qrOrderItemsSerializer(Machine machine) throws JSONException {
        JSONObject jsonItem = new JSONObject();

        jsonItem.put(
            "title",
            machine.getMachineType().equals("WASHER") ? "LAVADO LAVOMAT" : "SECADO LAVOMAT"
        );
        jsonItem.put("currency_id", "UYU");
        jsonItem.put(
            "description",
            machine.getMachineType().equals("WASHER") ? "LAVADO LAVOMAT" : "SECADO LAVOMAT"
        );
        jsonItem.put("quantity", 1.0);
        jsonItem.put("unit_price", machine.getMachineRate().getPriceCustomer(new Date()));

        ArrayList<JSONObject> items = new ArrayList<JSONObject>();
        items.add(jsonItem);

        return items;
    }

    private Transaction createQROrderTransaction(Machine machine) throws APIException {
        Transaction transaction = new Transaction(
            Currency.UYU,
            machine.getMachineRate().getPriceCustomer(null),
            null,
            machine,
            MercadoPagoTransactionFlow.QR_ORIGIN
        );
        transaction.save();

        return transaction;
    }

    /**
     * POST /api/v1/qr/notify
     * path set as `notification_url` on `createPOSorder`.
     * 1. update Transaction record
     * 2. deliver MQQT message
     * 1. create the first AuditWorkflow for 3.1
     * 3. schedule an async task - 1.5 minutes delay
     * 1. search an AuditWorkflow by the Transaction.id
     * 2. search a list of AuditWorkflows by the first AuditWorkflow found
     * 3. look for an AuditWorkflows with status completed.
     * The COMPLETED AuditWorkflow means the machine was activated successfully,
     * which is created on the push uses action.
     * Otherwise, make a refund (e.g. if there is an error while activation)
     */
    @Tx
    public F.Promise<Result> notifyTx(final String topic, final String id) throws Exception {
        play.Logger.debug("QR_NOTIFY_TRANSACTION>>> IPN_DATA: " + topic + "-" + id);
        play.Logger.debug("## QR FLOW ## notifyQRPayment ## topic: {} - id: {}", topic, id);

        // Primer paso en almacenar la informacion que llega de la IPN
        IpnNotification notif = new IpnNotification(topic, id);
        notif.save();
        play.Logger.debug(
            "## QR FLOW ## notifyQRPayment ## saving IPN - topic: {} - id: {} - ipn.id: {}",
            topic,
            id,
            notif.getId()
        );

        // Segundo paso es buscar el id de pago y obtener referencia externa (id de
        // transaccion interno)

        MercadoPago.SDK.setAccessToken(
            Play.application().configuration().getString("mercadopago.access.token")
        );
        MercadoPago.SDK.setClientId(
            Play.application().configuration().getString("mercadopago.client.id")
        );
        MercadoPago.SDK.setClientSecret(
            Play.application().configuration().getString("mercadopago.client.secret")
        );

        Payment payment = Payment.findById(id);
        play.Logger.debug("QR_NOTIFY_TRANSACTION>>> PAYMENT: " + payment.getStatusDetail());

        String transaction_id = "";
        String serial = "";

        play.Logger.debug(
            "## QR FLOW ## notifyQRPayment ## getting payment info from MP - topic: {} - id: {} - payment.ExternalReference: {} - payment.StatusDetail: {} - payment.AuthorizationCode: {} - payment.AuthorizationCode: {}",
            topic,
            id,
            payment.getExternalReference(),
            payment.getStatusDetail(),
            payment.getAuthorizationCode(),
            payment.getAuthorizationCode()
        );
        if (payment.getExternalReference() != null) {
            serial = payment.getExternalReference().split("_")[0];
            transaction_id = payment.getExternalReference().split("_")[1];
        }

        play.Logger.debug(
            "## QR FLOW ## notifyQRPayment ## transaction_id and serial? - topic: {} - id: {} - transaction_id: {} - machine_serial: {}",
            topic,
            id,
            transaction_id,
            serial
        );
        if (transaction_id != null && transaction_id != "") {
            Transaction internal_tx = getTransaction(transaction_id);

            play.Logger.debug(
                "## QR FLOW ## notifyQRPayment ## transaction found - topic: {} - id: {} - transaction_id: {}",
                topic,
                id,
                transaction_id
            );

            if (internal_tx == null) {
                play.Logger.error(
                    "## QR FLOW ## notifyQRPayment ## transaction NOT found by public id {}. Has this transaction been confirmed? does it have a bill associated?",
                    topic,
                    id,
                    transaction_id
                );
            }

            if (internal_tx != null && payment != null && payment.getStatusDetail() != null) {
                // Actualizar la transaccion interna con los datos del fallo
                play.Logger.debug(
                    "## QR FLOW ## notifyQRPayment ## updating transaction - topic: {} - id: {} - transaction_id: {}",
                    topic,
                    id,
                    transaction_id
                );

                internal_tx.setErrormessage(payment.getStatusDetail());
                internal_tx.setAuthorizationcode(payment.getStatus().name());
                internal_tx.setName(
                    payment.getPayer().getFirstName() + payment.getPayer().getLastName()
                );
                internal_tx.setEmail(
                    payment.getPayer().getEmail() != null ? payment.getPayer().getEmail() : ""
                );

                play.Logger.debug(
                    "## QR FLOW ## notifyQRPayment ## payment approved? - topic: {} - id: {} - payment.status.name: {}",
                    topic,
                    id,
                    payment.getStatus().name()
                );
                play.Logger.debug(
                    "## QR FLOW ## notifyQRPayment ## already billed? - topic: {} - id: {} - transaction.bill.id: {}",
                    topic,
                    id,
                    internal_tx.getBill() == null ? "No BILL" : internal_tx.getBill().getId()
                );
                if (
                    payment.getStatus().name().equals("approved") && internal_tx.getBill() == null
                ) {
                    // ACTIVAR CON RESULT 5
                    play.Logger.debug(
                        "## QR FLOW ## notifyQRPayment ## activating machine - topic: {} - id: {} - machine_serial: {}",
                        topic,
                        id,
                        serial
                    );

                    Machine machine = Machine.findBySerialNumber(serial);
                    if (machine == null) {
                        play.Logger.error(
                            "## QR FLOW ## notifyQRPayment ## Machine not found - topic: {} - id: {} - machine_serial: {}",
                            topic,
                            id,
                            serial
                        );
                        throw APIException
                            .raise(APIException.APIErrors.MACHINE_NOT_FOUND)
                            .setDetailMessage("MACHINE_NOT_FOUND");
                    }

                    POS pos = POS.findByMachineSerial(serial);
                    if (pos == null) {
                        play.Logger.error(
                            "## QR FLOW ## notifyQRPayment ## POS not found - topic: {} - id: {} - machine_serial: {}",
                            topic,
                            id,
                            serial
                        );
                        throw APIException
                            .raise(APIException.APIErrors.POS_NOT_FOUND)
                            .setDetailMessage("POS_NOT_FOUND");
                    }

                    Store store = pos.getStore();
                    if (store == null) {
                        play.Logger.error(
                            "## QR FLOW ## notifyQRPayment ## Store not found - topic: {} - id: {} - machine_serial: {}",
                            topic,
                            id,
                            serial
                        );
                        throw APIException
                            .raise(APIException.APIErrors.STORE_NOT_FOUND)
                            .setDetailMessage("STORE_NOT_FOUND");
                    }

                    final int tx = internal_tx.getId();
                    MachineActivationService
                        .instance()
                        .atBuilding(store.getBuilding())
                        .forMachine(machine)
                        .sendingMqttMessage()
                        .throughTransaction(internal_tx)
                        .viaQR()
                        .activate();
                    play.Logger.debug(
                        "## QR FLOW ## notifyQRPayment ## machine activated - topic: {} - id: {} - machine_serial: {} - transaction.id: {} - building.id: {}",
                        topic,
                        id,
                        serial,
                        transaction_id,
                        store.getBuilding().getId()
                    );

                    new java.util.Timer()
                        .schedule(
                            new java.util.TimerTask() {
                                @Override
                                public void run() {
                                    try {
                                        JPA.withTransaction(() -> {
                                            play.Logger.debug(
                                                "## QR FLOW ## notifyQRPayment ## scheduled task ## looking for notifications of a succeed activation - topic: {} - id: {} - transaction.id: {}",
                                                topic,
                                                id,
                                                tx
                                            );
                                            play.Logger.debug("QR_TX>>>" + tx);
                                            AuditWorkflow a = AuditWorkflow.findByPaymentTx(tx);
                                            play.Logger.debug(
                                                "## QR FLOW ## notifyQRPayment ## scheduled task ## AuditWorkflow found - topic: {} - id: {} - transaction.id: {} - auditWorkflow.id: {}",
                                                topic,
                                                id,
                                                tx,
                                                a.getId()
                                            );
                                            play.Logger.debug("AUDIT-WORKFLOW>>>" + a.getId());
                                            List<AuditWorkflow> list = AuditWorkflow.findByOriginalId(
                                                a.getId()
                                            );

                                            boolean is_completed = false;
                                            for (AuditWorkflow aw : list) {
                                                play.Logger.debug("AW-ITEM-LIST>>>" + aw.getId());
                                                if (
                                                    aw.getStatus() == AuditWorkflow.Status.COMPLETED
                                                ) {
                                                    play.Logger.debug("COMPLETED>>>");
                                                    is_completed = true;
                                                }
                                            }

                                            play.Logger.debug(
                                                "## QR FLOW ## notifyQRPayment ## scheduled task ## AuditWorkflow completed? - topic: {} - id: {} - transaction.id: {} - auditWorkflow.id: {} - completed: {}",
                                                topic,
                                                id,
                                                tx,
                                                a.getId(),
                                                is_completed
                                            );
                                            if (!is_completed) {
                                                play.Logger.debug(
                                                    "## QR FLOW ## notifyQRPayment ## scheduled task ## refund initiated - topic: {} - id: {} - transaction.id: {} - auditWorkflow.id: {}",
                                                    topic,
                                                    id,
                                                    tx,
                                                    a.getId()
                                                );
                                                // DEVOLUCION
                                                URL url = null;
                                                try {
                                                    url =
                                                        new URL(
                                                            "https://api.mercadopago.com/v1/payments/" +
                                                            payment.getId() +
                                                            "/refunds?access_token=" +
                                                            Play
                                                                .application()
                                                                .configuration()
                                                                .getString(
                                                                    "mercadopago.access.token"
                                                                )
                                                        );
                                                    play.Logger.debug("REFUND>>>" + url);
                                                } catch (MalformedURLException e) {
                                                    e.printStackTrace();
                                                }

                                                HttpURLConnection conn = null;
                                                try {
                                                    conn = (HttpURLConnection) url.openConnection();
                                                } catch (IOException e) {
                                                    e.printStackTrace();
                                                }

                                                conn.setDoOutput(true);

                                                try {
                                                    conn.setRequestMethod("POST");
                                                } catch (ProtocolException e) {
                                                    e.printStackTrace();
                                                }

                                                conn.setRequestProperty(
                                                    "accept",
                                                    "application/json"
                                                );
                                                conn.setRequestProperty(
                                                    "content-type",
                                                    "application/json; utf-8"
                                                );

                                                try {
                                                    if (
                                                        conn.getResponseCode() !=
                                                        HttpURLConnection.HTTP_OK &&
                                                        conn.getResponseCode() !=
                                                        HttpURLConnection.HTTP_CREATED
                                                    ) {
                                                        throw new RuntimeException(
                                                            "Failed : HTTP error code : " +
                                                            conn.getResponseCode()
                                                        );
                                                    }
                                                } catch (IOException e) {
                                                    e.printStackTrace();
                                                }

                                                BufferedReader br = null;

                                                try {
                                                    br =
                                                        new BufferedReader(
                                                            new InputStreamReader(
                                                                (conn.getInputStream())
                                                            )
                                                        );
                                                } catch (IOException e) {
                                                    e.printStackTrace();
                                                }

                                                String output;
                                                StringBuilder sb = new StringBuilder();

                                                try {
                                                    while ((output = br.readLine()) != null) {
                                                        sb.append(output);
                                                    }
                                                } catch (IOException e) {
                                                    e.printStackTrace();
                                                }

                                                play.Logger.debug("RESPONSE>>> " + sb.toString());

                                                String resp_aux = sb.toString();
                                            }
                                        });
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        try {
                                            throw APIException
                                                .raise(
                                                    APIException.APIErrors.INTERNAL_SERVER_ERROR,
                                                    e
                                                )
                                                .withParentDetailMessage()
                                                .withParentStackTrace();
                                        } catch (APIException e1) {
                                            // TODO Auto-generated catch block
                                            e1.printStackTrace();
                                        }
                                    }
                                }
                            },
                            90000
                        );
                }
            }
        }
        JSONObject response = new JSONObject();
        response.put("result", "NOTIFIED");
        return json(response.toString());
    }
}
