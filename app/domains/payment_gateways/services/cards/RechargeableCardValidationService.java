package domains.payment_gateways.services.cards;

import global.APIException;
import models.Card;
import models.Part;
import models.Unit;
import org.apache.commons.lang3.StringUtils;
import services.BaseService;
import utils.NumberHelper;

public class RechargeableCardValidationService extends BaseService {

    private final Card card;

    public RechargeableCardValidationService(Card card) {
        assert card != null;

        this.card = card;
    }

    /**
     * Given a card return if it is able to increase its balance.
     *
     * @throws APIException in order to indicate api-clients a message and code error.
     */
    public boolean validate() throws APIException {
        if (Card.ContractType.POSTPAID == this.card.getContractType()) {
            return validatePostpaid();
        }

        return validatePrepaid();
    }

    private boolean validatePostpaid() throws APIException {
        assert Card.ContractType.POSTPAID == this.card.getContractType();

        String message = "";

        Unit unit = this.card.getUnit();
        if (unit != null) {
            String price = NumberHelper.formatDoubleWithSeparators(unit.getRatePriceCostumer());
            message =
                "Su tarjeta es POSTPAGO, no requiere precarga. Los usos realizados con su tarjeta se cobran en los gastos comunes.";
            if (StringUtils.isNotBlank(price)) {
                message += " El costo de cada uso es de $" + price + ".";
            }
        }

        throw APIException.raise(APIException.APIErrors.CARD_NOT_PREPAID).setDetailMessage(message);
    }

    private boolean validatePrepaid() throws APIException {
        assert Card.ContractType.PREPAID == this.card.getContractType();

        if (Part.PartState.ACTIVE != this.card.getState()) {
            if (Part.PartState.LOST == this.card.getSubState()) {
                throw APIException.raise(APIException.APIErrors.CARD_LOST);
            }

            if (Part.PartState.SUSPENDED == this.card.getSubState()) {
                throw APIException.raise(APIException.APIErrors.CARD_SUSPENDED);
            }

            throw APIException.raise(APIException.APIErrors.INACTIVE_CARD);
        }

        boolean cardDoesNotBelongToUnit = this.card.getUnit() == null;
        if (cardDoesNotBelongToUnit && !this.card.isVirtual()) {
            throw APIException.raise(APIException.APIErrors.CARD_NOT_FROM_BUILDING);
        }

        return true;
    }
}
