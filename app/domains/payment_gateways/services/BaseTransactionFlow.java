package domains.payment_gateways.services;

import domains.billing.BillingService;
import domains.billing.exceptions.BillingException;
import domains.billing.invoicing.InvoicingService;
import domains.billing.invoicing.InvoicingServiceFactory;
import domains.billing.invoicing.exceptions.InvoicingException;
import domains.payment_gateways.exceptions.LavomatCommissionExceedsAmountException;
import global.APIException;
import global.exceptions.UnavailableOperation;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import models.*;
import models.Audit.ResultCode;
import org.apache.commons.lang3.StringUtils;
import play.db.jpa.JPA;
import queries.transactions.TransactionQuery;
import services.BaseService;
import utils.ApplicationConfiguration;
import utils.StringHelper;
import utils.email.EmailService;
import utils.email.LavamarFromProps;

public class BaseTransactionFlow extends BaseService {

    protected BillingService billingService;

    public BaseTransactionFlow() {
        billingService = new BillingService();
    }

    public Bill createBill(double amount, Currency currency, Transaction transaction)
        throws BillingException {
        return createBillWithAssociatedRecipient(amount, currency, transaction, null);
    }

    /**
     * This method associates a CFERecipient to bills to be able to set a
     * specific branch number into the bill.
     */
    public Bill createBillWithAssociatedRecipient(
        double amount,
        Currency currency,
        Transaction transaction,
        Building associatedRecipient
    ) throws BillingException {
        logger("Billing transaction # transaction.id: {}", transaction.getId());
        List<BillItem> items = new ArrayList<>();

        String uid = transaction.getUid();
        String detalle = "";
        boolean isBrandingItem = transaction.getReasonType() == Transaction.ReasonType.BRANDING;
        Integer quantity = 1;

        logger(
            "Billing transaction # isBrandingItem? - transaction.id: {} - isBrandingItem: {}",
            transaction.getId(),
            isBrandingItem
        );
        BillItem item = new BillItem();
        if (isBrandingItem) {
            detalle = "Pago por accesorio/s Lavomat";
            quantity = transaction.getQuantity();
            item.setItemMeasureUnit(BillItem.MEASURE_BRANDING_ITEM);
        } else if (StringUtils.isNotBlank(uid)) {
            if (uid.startsWith("0x") || uid.startsWith("0X")) uid = uid.substring(2);

            detalle = "Carga de saldo a la uid " + uid;
            item.setItemMeasureUnit(BillItem.MEASURE_UNIT_BALANCE);
        } else {
            String companyName = getTransactionLaundromatCompany(transaction);
            detalle = "Pago por servicio de lavado/secado " + companyName;
            item.setItemMeasureUnit(BillItem.MEASURE_UNIT_USE);
        }

        item.setAmount(quantity);
        item.setItemName(detalle);

        logger(
            "Billing transaction # splitting? - transaction.id: {} - transaction.comission: {}",
            transaction.getId(),
            transaction.getComission()
        );
        // cuando aplica splitting se debe facturar solo la comision de lavomat
        if (transaction.getComission() != 0) {
            item.setItemUnitPrice(transaction.getComission() / 1.22);
        } else if (isBrandingItem) {
            item.setItemUnitPrice((amount / quantity) / 1.22);
        } else {
            item.setItemUnitPrice(amount / 1.22);
        }

        item.setItemIndDet(3);

        items.add(item);

        Unit unit = transaction.getUnit();

        Bill bill = null;

        if (unit != null) {
            logger(
                "Billing transaction # billing unit - transaction.id: {} - unit.id: {}",
                transaction.getId(),
                unit.getId()
            );
            bill = new Bill(unit, items, null, null);
            bill.setCurrency(currency.getIsoCode());

            // Si la transaccion tiene un rut, se prioriza frente a la de la unit
            if (StringUtils.isNotBlank(transaction.getRut())) {
                bill.setBillToType(Bill.BILL_TO_TYPE_MISC);
                bill.setRecipientTipoDoc(Bill.DOC_TYPE_RUT);
                bill.setRecipientRut(transaction.getRut());
            }

            finalizeBill(bill, transaction, associatedRecipient, "BILL");
        } else {
            // si no tiene uuid puede tratarse o bien de una uuid virtual o de una trx del totem
            logger(
                "Billing transaction # billing anonymous - transaction.id: {}",
                transaction.getId()
            );

            boolean hasRut = StringUtils.isNotBlank(transaction.getRut());
            String docType = hasRut ? Bill.DOC_TYPE_RUT : null;
            String rut = hasRut ? transaction.getRut() : null;

            bill =
                new Bill(
                    "",
                    docType,
                    rut,
                    "",
                    "",
                    "",
                    "UY",
                    "UY",
                    items,
                    null,
                    null,
                    new Date(),
                    currency.getIsoCode(),
                    null
                );

            finalizeBill(bill, transaction, associatedRecipient, "BILL_NO_UNIT");
        }

        return bill;
    }

    public double lavomatCommission(Rate rate, double amount) throws APIException {
        double commissionAdministration = rate.getPriceCompany() / rate.getPriceCustomer();
        double commissionLavomat = amount * commissionAdministration;

        if (commissionLavomat > amount) {
            throw new LavomatCommissionExceedsAmountException();
        }

        logger("CREATE_TRANSACTION >>> COMISION FINAL" + commissionLavomat);

        return commissionLavomat;
    }

    public Bill createBillTaxFree(double amount, Integer currency, Transaction transaction)
        throws BillingException {
        List<BillItem> items = new ArrayList<>();

        BillItem item = new BillItem();
        item.setAmount(1);
        item.setItemName("Serv. por Cta. 3ros - SV NF (sin IVA)");
        item.setItemMeasureUnit("N/A");
        item.setItemUnitPrice(amount);
        item.setItemMeasureUnit(BillItem.MEASURE_UNIT_BALANCE);
        item.setItemIndDet(ItemBillingIndicator.EXENTO_DE_IVA.getCode());

        items.add(item);

        Unit unit = transaction.getUnit();

        Bill bill = null;

        if (unit != null) {
            bill = new Bill(unit, items, null, null);
            bill.setCurrency(Currency.findByIsoNumber(currency).getIsoCode());
            bill.setCashPaymentMethod();

            // Si la transaccion tiene un rut, se prioriza frente a la de la unit, se lo
            // seteo en memoria

            if (!StringHelper.isBlank(transaction.getRut())) {
                bill.setBillToType(Bill.BILL_TO_TYPE_MISC);
                bill.setRecipientTipoDoc(Bill.DOC_TYPE_RUT);
                bill.setRecipientRut(transaction.getRut());
            }
            bill.setTransaction(transaction);
            bill.setMainBill(false);
            bill.save();

            billingService.publishBill(bill);

            logger("BILL >>>" + bill.getNumber());
        }

        return bill;
    }

    public Bill createBillTaxFreeThroughKiosk(
        double amount,
        Integer currency,
        Transaction transaction
    ) throws BillingException {
        List<BillItem> items = new ArrayList<>();

        BillItem item = new BillItem();
        item.setAmount(1);
        item.setItemName("Serv. por Cta. 3ros - SV NF (sin IVA)");
        item.setItemMeasureUnit("N/A");
        item.setItemUnitPrice(amount);
        item.setItemMeasureUnit(BillItem.MEASURE_UNIT_USE);

        item.setItemIndDet(ItemBillingIndicator.EXENTO_DE_IVA.getCode());

        items.add(item);

        Bill bill = null;

        boolean isBusinessRecipient = StringUtils.isNotBlank(transaction.getRut());
        String recipientName = isBusinessRecipient ? transaction.getName() : StringUtils.EMPTY;
        String recipientDocType = isBusinessRecipient ? Bill.DOC_TYPE_RUT : null;
        String recipientDocument = isBusinessRecipient ? transaction.getRut() : null;
        String recipientAddress = isBusinessRecipient
            ? transaction.getAddress()
            : StringUtils.EMPTY;

        bill =
            new Bill(
                recipientName,
                recipientDocType,
                recipientDocument,
                recipientAddress,
                "",
                "",
                "UY",
                "UY",
                items,
                null,
                null,
                new Date(),
                Currency.findByIsoNumber(currency).getIsoCode(),
                null
            );
        bill.setCashPaymentMethod();
        bill.setTransaction(transaction);
        bill.setMainBill(false);
        bill.save();

        billingService.publishBill(bill);

        logger("KIOSK - BILL >>>" + bill.getNumber());

        return bill;
    }

    public void sendBillPDFAsync(Transaction transaction, Bill... bills) {
        if (StringUtils.isBlank(transaction.getEmail())) {
            return;
        }

        new Thread(
            (
                new Runnable() {
                    int transactionId;
                    List<Bill> bills;

                    public Runnable init(int transactionId, List<Bill> bills) {
                        this.transactionId = transactionId;
                        this.bills = bills;

                        return this;
                    }

                    @Override
                    public void run() {
                        JPA.withTransaction(() -> {
                            logger("Sending bill pdf by email async...");
                            try {
                                List<Bill> bills = this.bills;
                                Transaction transaction = Transaction.findById(this.transactionId);
                                sendBillPDF(transaction, (Bill) bills);

                                logger("Sent bill pdf by email async.");
                            } catch (Exception e) {
                                loggerError(
                                    "Error while sending bill pdf by email async - error: {}",
                                    e.getMessage()
                                );
                                e.printStackTrace();
                            }
                        });
                    }
                }
            ).init(transaction.getId(), Arrays.asList(bills))
        )
            .start();
    }

    @Deprecated
    public void sendBillPDF(Transaction transaction, Bill... bills) {
        if (StringUtils.isBlank(transaction.getEmail()) || bills == null || bills.length == 0) {
            return;
        }

        try {
            // Here every bill is billed to the same tenant
            InvoicingService invoicingService = InvoicingServiceFactory.createService(bills[0]);
            List<Attachment> billsToSend = new ArrayList<>();
            for (Bill bill : bills) {
                if (bill == null) continue;
                try {
                    byte[] billPdfData = invoicingService.getInvoiceFile(bill);

                    String attachementName = "Factura_" + bill.getSerie() + "_" + bill.getNumber();

                    billsToSend.add(
                        new Attachment(attachementName, "pdf", "application/pdf", billPdfData)
                    );
                } catch (InvoicingException e) {
                    e.printStackTrace();

                    loggerError(
                        "ERROR - Thrown error during trying to send bill by email, bill id: {} - error: {}.",
                        bill.getId(),
                        e.getMessage()
                    );

                    throw APIException
                        .raise(APIException.APIErrors.INTERNAL_SERVER_ERROR, e)
                        .withParentDetailMessage()
                        .withParentStackTrace();
                }
            }

            if (
                transaction.getBuilding() != null &&
                BuildingType.THIRD_PARTY_LAUNDROMAT.equals(
                    transaction.getBuilding().getBuildingType()
                )
            ) {
                String body = views.html.generalMessage
                    .render("Su pago se ha realizado con éxito", transaction.getEmail())
                    .body();
                EmailService.send(
                    transaction.getEmail(),
                    new LavamarFromProps(),
                    "Compra confirmada!",
                    body,
                    billsToSend
                );
            } else {
                String body = getBodyTransaction(transaction);
                EmailService.send(transaction.getEmail(), "Compra confirmada!", body, billsToSend);
            }

            transaction.setSendBill(true);
        } catch (Exception e) {
            transaction.setSendBill(false);
            e.printStackTrace();
        } finally {
            transaction.update();
        }
    }

    public byte[] generateBillPDF(Bill bill) {
        byte[] pdf = null;
        InvoicingService invoicingService = InvoicingServiceFactory.createService(bill);
        try {
            pdf = invoicingService.getInvoiceFile(bill);
        } catch (NumberFormatException | InvoicingException e) {
            e.printStackTrace();
        }

        return pdf;
    }

    public String getBodyTransaction(Transaction transaction) {
        String currency = transaction.getCurrency().toString();
        String amount = transaction.getAmount() + "";
        String uid = transaction.getUid();
        String email = transaction.getEmail();

        if (uid != null) {
            if (uid.startsWith("0x") || uid.startsWith("0X")) uid = uid.substring(2);

            Card card = null;

            if (StringUtils.isNotBlank(uid)) {
                if (
                    uid.length() > 1 &&
                    uid.length() < 10 &&
                    !uid.startsWith("0x") &&
                    !uid.startsWith("0X")
                ) uid = "0x" + uid;

                card = Card.findByUID(uid);
            }

            if (uid.startsWith("0x") || uid.startsWith("0X")) {
                uid = uid.substring(2);
            }

            if (transaction.getComission() == 0) {
                return views.html.transactionNotification
                    .render(currency, amount, uid, email, Double.toString(card.getBalance()))
                    .body();
            } else {
                return views.html.transactionNotificationSplitting
                    .render(
                        currency,
                        amount,
                        Double.toString(transaction.getComission()),
                        Double.toString(
                            Math.round(
                                (Double.parseDouble(amount) - transaction.getComission()) * 100.0
                            ) /
                            100.0
                        ),
                        uid,
                        email,
                        Double.toString(card.getBalance()),
                        card.getUnit().getBuilding().getName(),
                        Double.toString(
                            card.getUnit().getBuilding().getRate().getPriceCustomer(new Date())
                        )
                    )
                    .body();
            }
        } else {
            // another mail
            return views.html.generalMessage
                .render("Su pago se ha realizado con éxito", email)
                .body();
        }
    }

    public Bill createBillBeforeConfirmation(
        double amount,
        Integer currency,
        Transaction transaction
    ) throws BillingException {
        return createBillBeforeConfirmationWithAssociatedRecipient(
            amount,
            currency,
            transaction,
            null
        );
    }

    private String getTransactionLaundromatCompany(Transaction transaction) {
        Building building = transaction.getBuilding();
        if (building != null && building.getBuildingType() == BuildingType.THIRD_PARTY_LAUNDROMAT) {
            return building.getName();
        }

        return "Lavomat";
    }

    /**
     * This method associates a CFERecipient to bills to be able to set a
     * specific branch number into the bill.
     */
    public Bill createBillBeforeConfirmationWithAssociatedRecipient(
        double amount,
        Integer currency,
        Transaction transaction,
        Building associatedRecipient
    ) throws BillingException {
        // genera factura SIN numerar para reserva posterior

        List<BillItem> items = new ArrayList<>();

        String uid = transaction.getUid();
        String detalle = "";

        if (uid != null) {
            if (uid.startsWith("0x") || uid.startsWith("0X")) uid = uid.substring(2);
            detalle = "Carga de saldo a la uid " + uid;
        } else {
            String companyName = getTransactionLaundromatCompany(transaction);
            detalle = "Pago por servicio de lavado/secado " + companyName;
        }

        BillItem item = new BillItem();
        item.setAmount(1);
        item.setItemName(detalle);
        item.setItemMeasureUnit(BillItem.MEASURE_UNIT_BALANCE);
        // item.setItemUnitPrice(amount / 1.22);

        // cuando aplica splitting se debe facturar solo la comision de lavomat
        if (transaction.getComission() != 0) {
            item.setItemUnitPrice(transaction.getComission() / 1.22);
        } else {
            item.setItemUnitPrice(amount / 1.22);
        }

        item.setItemIndDet(3);

        items.add(item);

        Bill bill = null;

        String tipo_doc = transaction.getRut() != null ? Bill.DOC_TYPE_RUT : null;
        String nro_doc = transaction.getRut() != null ? transaction.getRut() : null;

        bill =
            new Bill(
                "",
                tipo_doc,
                nro_doc,
                "",
                "",
                "",
                "UY",
                "UY",
                items,
                null,
                null,
                new Date(),
                Currency.findByIsoNumber(currency).getIsoCode(),
                null
            );
        bill.setAssociatedRecipient(associatedRecipient);
        bill.setTransaction(transaction);
        bill.save();

        billingService.publishBill(bill);

        transaction.setBill(bill);
        transaction.update();

        return bill;
    }

    public Bill createBillForReservation(Transaction transaction) throws BillingException {
        return createBillForReservationWithAssociatedRecipient(transaction, null);
    }

    public Bill createBillForReservationWithAssociatedRecipient(
        Transaction transaction,
        Building associatedRecipient
    ) throws BillingException {
        List<BillItem> items = new ArrayList<>();

        String uid = transaction.getUid();
        String detalle = "";

        BillItem item = new BillItem();
        if (uid != null) {
            if (uid.startsWith("0x") || uid.startsWith("0X")) uid = uid.substring(2);
            detalle = "Carga de saldo a la uid " + uid;
            item.setItemMeasureUnit(BillItem.MEASURE_UNIT_BALANCE);
        } else {
            String companyName = getTransactionLaundromatCompany(transaction);
            detalle = "Pago por servicio de lavado/secado " + companyName;
            item.setItemMeasureUnit(BillItem.MEASURE_UNIT_USE);
        }

        item.setAmount(1);
        item.setItemName(detalle);

        // cuando aplica splitting se debe facturar solo la comision de lavomat
        if (transaction.getComission() != 0) {
            item.setItemUnitPrice(transaction.getComission() / 1.22);
        } else {
            item.setItemUnitPrice(transaction.getAmount() / 1.22);
        }

        item.setItemIndDet(3);
        items.add(item);

        Bill bill = null;

        String currencyIsoCode = Currency
            .findByIsoNumber(transaction.getCurrency().getIsoNumber())
            .getIsoCode();

        Unit unit = transaction.getUnit();
        if (unit != null) {
            bill = new Bill(unit, items, null, null);
            bill.setCurrency(currencyIsoCode);
            bill.setCashPaymentMethod();

            // Si la transaccion tiene un rut, se prioriza frente a la de la unit, se lo
            // seteo en memoria
            if (StringUtils.isNotBlank(transaction.getRut())) {
                bill.setBillToType(Bill.BILL_TO_TYPE_MISC);
                bill.setRecipientTipoDoc(Bill.DOC_TYPE_RUT);
                bill.setRecipientRut(transaction.getRut());
                bill.setRecipientName(transaction.getName());
                bill.setRecipientAddress(transaction.getAddress());
            }
        } else {
            boolean isBusinessRecipient = StringUtils.isNotBlank(transaction.getRut());
            String recipientName = isBusinessRecipient ? transaction.getName() : StringUtils.EMPTY;
            String recipientDocType = isBusinessRecipient ? Bill.DOC_TYPE_RUT : null;
            String recipientDocument = isBusinessRecipient ? transaction.getRut() : null;
            String recipientAddress = isBusinessRecipient
                ? transaction.getAddress()
                : StringUtils.EMPTY;

            bill =
                new Bill(
                    recipientName,
                    recipientDocType,
                    recipientDocument,
                    recipientAddress,
                    "",
                    "",
                    "UY",
                    "UY",
                    items,
                    null,
                    null,
                    new Date(),
                    currencyIsoCode,
                    null
                );
            bill.setCashPaymentMethod();
        }
        bill.setAssociatedRecipient(associatedRecipient);
        bill.setTransaction(transaction);
        bill.save();

        transaction.setBill(bill);
        transaction.update();

        return bill;
    }

    protected void updateCards(Transaction transaction, double amount) {
        String uid = transaction.getUid();

        Card card = Card.findByUID(uid);

        if (card != null && card.getContractType() == Card.ContractType.PREPAID) {
            logger(
                "Billing transaction # updating card - transaction.id: {} - UUID >>> {}",
                transaction.getId(),
                uid
            );

            double previousBalance = card.getBalance();
            card.setBalance(previousBalance + amount);
            card.update();

            logger(
                "Billing transaction # card updated - transaction.id: {} - UUID >>> {} - previous balance {} - new balance {}",
                transaction.getId(),
                uid,
                previousBalance,
                card.getBalance()
            );

            Audit audit = new Audit(
                Audit.TransferType.CREDIT,
                amount,
                Currency.UYU,
                card.getUuid().toString(),
                card.getUuid().toString(),
                "COMPRA DE CREDITO",
                0,
                ResultCode.OK,
                previousBalance,
                card.getBalance()
            );
            audit.save();

            logger(
                "Billing transaction # AUDIT card updated - transaction.id: {} - UUID >>> {} - audit.id {} - previous balance {} - new balance {}",
                transaction.getId(),
                uid,
                audit.getId(),
                audit.getPreviousBalance(),
                audit.getNewBalance()
            );
        }
    }

    @Override
    protected String getLoggerClassSuffix() {
        return "TransactionFlow";
    }

    public final void handleException(Exception e) throws APIException {
        logger("An error happened during the operation.");

        handleInternalException(e);

        if (e instanceof APIException) {
            throw (APIException) e;
        } else {
            throw APIException
                .raise(APIException.APIErrors.INTERNAL_SERVER_ERROR, e)
                .withParentDetailMessage()
                .withParentStackTrace();
        }
    }

    protected void handleInternalException(Exception ex) throws APIException {
        // this method should be implemented in children who need special exceptions
        // handling
    }

    /**
     * Helper method to finalize bill creation with common operations
     */
    private void finalizeBill(
        Bill bill,
        Transaction transaction,
        Building associatedRecipient,
        String logPrefix
    ) {
        bill.setCashPaymentMethod();
        bill.setAssociatedRecipient(associatedRecipient);
        bill.setTransaction(transaction);
        bill.save();

        logger(
            "Billing transaction # bill saved - transaction.id: {} - bill.id: {}",
            transaction.getId(),
            bill.getId()
        );

        billingService.publishBill(bill);

        transaction.setBill(bill);
        transaction.update();

        logger(logPrefix + " >>>" + bill.getNumber());
        logger(
            "Billing transaction # bill published - transaction.id: {} - bill.id: {} - bill.number: {}",
            transaction.getId(),
            bill.getId(),
            bill.getNumber()
        );
    }

    /**
     * Validate if creating a new transactions is enabled.
     */
    protected void createCreditTransaction() throws APIException {
        if (ApplicationConfiguration.arePaymentGatewaysTransactionsDisabled()) {
            logger("Temporal disabled");
            throw new UnavailableOperation();
        }
    }

    /**
     * Validate if creating a new transactions is enabled.
     */
    protected void createBrandingTransaction() throws APIException {
        if (ApplicationConfiguration.arePaymentGatewaysTransactionsDisabled()) {
            logger("Temporal disabled");
            throw new UnavailableOperation();
        }
    }

    protected Transaction getTransaction(String publicId) {
        return new TransactionQuery().filterByPublicId(publicId).single();
    }

    protected Transaction getTransactionByProviderId(String providerId) {
        return new TransactionQuery().filterByProviderTransactionId(providerId).single();
    }
}
