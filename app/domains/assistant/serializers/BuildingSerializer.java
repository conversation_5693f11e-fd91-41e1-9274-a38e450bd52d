package domains.assistant.serializers;

import java.util.List;
import models.Building;
import org.json.JSONObject;
import policies.Level;
import serializers.BaseSerializer;

public class BuildingSerializer extends BaseSerializer<Building> {

    public static final String HAS_SOAP_DISPENSERS_ATTRIBUTE = "hasSoapDispensers";

    public BuildingSerializer(Building entity) {
        super(entity);
    }

    public BuildingSerializer(List<Building> entities) {
        super(entities, "buildings");
    }

    protected JSONObject itemToJson(Building building, int level) throws Throwable {
        JSONObject buildingJson = new JSONObject();

        buildingJson.put("name", building.getName());
        buildingJson.put("slug", building.getSlug());

        if (level >= Level.MINIMAL_MEDIUM_LEVEL) {
            buildingJson.put("city", building.getCity());
            buildingJson.put("type", building.getBuildingType());
            buildingJson.put("isRemoteActivationEnabled", building.isRemoteActivationEnabled());

            this.setAdditionalParam(buildingJson, building, HAS_SOAP_DISPENSERS_ATTRIBUTE);

            buildingJson.put(
                "rate",
                new RateSerializer(building.getRate()).toJson(Level.MINIMAL_MEDIUM_LEVEL)
            );

            JSONObject googleMaps = new JSONObject();
            googleMaps.put("name", building.getBuildingSetting().getGoogleMapsDescription());
            googleMaps.put("link", building.getBuildingSetting().getGoogleMapsLink());
            buildingJson.put("googleMaps", googleMaps);
        }

        return buildingJson;
    }
}
