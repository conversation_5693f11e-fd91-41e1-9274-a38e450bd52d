package domains.assistant.serializers.buildings;

import java.util.List;
import org.json.JSONObject;
import policies.Level;
import serializers.BaseSerializer;
import services.building.BuildingPricingService;

public class PricingRuleSerializer extends BaseSerializer<BuildingPricingService.PricingRule> {

    public PricingRuleSerializer(BuildingPricingService.PricingRule entity) {
        super(entity);
    }

    public PricingRuleSerializer(List<BuildingPricingService.PricingRule> entities) {
        super(entities, "pricing");
    }

    @Override
    protected JSONObject itemToJson(BuildingPricingService.PricingRule entity, int level)
        throws Throwable {
        JSONObject json = new JSONObject();

        json.put("name", entity.getName());

        if (level >= Level.MINIMAL_MEDIUM_LEVEL) {
            json.put("capacity", entity.getCapacity());
            json.put("priceCustomer", entity.getPriceCustomer());
            json.put("hasSoapDispenser", entity.hasSoapDispenser());
        }

        return json;
    }
}
