package domains.assistant.controllers.v1.buildings;

import com.play4jpa.jpa.db.Tx;
import domains.assistant.controllers.v1.AssistantBaseController;
import domains.assistant.dto.buildings.GetBuildingBySlugParameters;
import domains.assistant.serializers.MachineSerializer;
import global.APIException;
import java.util.List;
import models.Machine;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import policies.Level;
import policies.MachinePolicy;
import policies.actions.Policy;
import queries.soap_dispensers.SoapDispenserQuery;
import security.v1.Secured;
import services.machine.MachineUsabilityService;

@Policy(MachinePolicy.class)
@Security.Authenticated(Secured.class)
public class MachinesController extends AssistantBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list(String slug) throws APIException {
        MachinePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        GetBuildingBySlugParameters params = new GetBuildingBySlugParameters(slug).validate();
        services.building.MachineAvailabilityService service = new services.building.MachineAvailabilityService(
            params.getBuilding()
        );

        List<Machine> machines = service.getMachines();

        return json(
            new MachineSerializer(machines)
                .append(
                    MachineSerializer.STATUS_ATTRIBUTE,
                    m -> new MachineUsabilityService(m).getStatus()
                )
                .append(
                    MachineSerializer.REMAINING_TIME_ATTRIBUTE,
                    m -> new MachineUsabilityService(m).getRemainingTime()
                )
                .append(
                    MachineSerializer.HAS_SOAP_DISPENSERS_ATTRIBUTE,
                    m -> new SoapDispenserQuery().filterByMachineId(m.getId()).any()
                )
                .toJsonList(queryLevel(Level.MINIMAL_MEDIUM_LEVEL))
        );
    }
}
