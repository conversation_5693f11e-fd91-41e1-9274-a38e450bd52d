package domains.webhooks.services;

import domains.webhooks.dto.SentryTotemAlertParameters;
import global.APIException;
import org.apache.commons.lang3.StringUtils;
import services.notification.SlackService;

public class SentryAlertService extends services.BaseService {

    public void notifyAlert(SentryTotemAlertParameters dto) throws APIException {
        SlackService slack = new SlackService();

        String message =
            ":rotating_light: *A new issues has been reported in Sentry* \n" +
            StringUtils.capitalize(dto.getErrorLevel()) +
            " | <" +
            dto.getErrorUrl() +
            "|" +
            dto.getErrorTitle() +
            "> \n" +
            "Thrown by the alert _" +
            dto.getAlert() +
            "_.";

        slack.notifyChannel(SlackService.Channel.ALERTS_TOTEM, message);
    }
}
