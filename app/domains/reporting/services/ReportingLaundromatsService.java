package domains.reporting.services;

import static utils.DateHelper.getMonthName;
import static utils.DateHelper.printMonth;

import domains.reporting.builders.PuntaShoppingMonthlyReportBuilder;
import domains.reporting.builders.ReportBuilder;
import java.util.ArrayList;
import java.util.List;
import models.Building;
import models.BuildingReportingSchedule;
import models.MachineUse;
import models.MachineUseResult;
import org.joda.time.DateTime;
import org.joda.time.DateTimeConstants;
import play.db.jpa.JPA;
import queries.machine_uses.MachineUseQuery;
import utils.DateHelper;
import utils.NumberHelper;
import utils.SQLHelper;
import utils.email.EmailService;

public class ReportingLaundromatsService extends services.BaseService {

    public void doDailyReport(Building building, BuildingReportingSchedule schedule) {
        DateTime yesterdayAtNoon = DateTime.now().minusDays(1).withTime(0, 0, 0, 0);
        DateTime yesterdayAtMidnight = DateTime.now().minusDays(1).withTime(23, 59, 59, 0);

        long machineUseCount = new MachineUseQuery()
            .filterForBillingByBuildingId(
                yesterdayAtNoon.toDate(),
                yesterdayAtMidnight.toDate(),
                building.getId(),
                MachineUse.ACCREDITED_RESULT.toArray(new MachineUseResult[0])
            )
            .count();

        Double priceCustomer = building.getRate().getPriceCustomer(yesterdayAtMidnight.toDate());

        Double totalInSales = machineUseCount * priceCustomer;

        String body = views.html.reporting.dailyReport
            .render(
                DateHelper.printDate(yesterdayAtMidnight),
                NumberHelper.formatDoubleWithSeparators(totalInSales)
            )
            .body();

        try {
            EmailService.sendAsync(
                schedule.getReportingEmail(),
                "Informe de ventas – Cierre diario",
                body
            );
        } catch (Exception e) {
            loggerError(
                "Error while sending daily report email for building {}: ",
                building.getId(),
                e.getMessage()
            );
        }
    }

    public void doWeeklyReport(Building building, BuildingReportingSchedule schedule) {
        DateTime sevenDaysAgoAtNoon = DateTime.now().minusDays(7).withTime(0, 0, 0, 0);
        DateTime yesterdayAtMidnight = DateTime.now().minusDays(1).withTime(23, 59, 59, 0);

        long machineUseCount = new MachineUseQuery()
            .filterForBillingByBuildingId(
                sevenDaysAgoAtNoon.toDate(),
                yesterdayAtMidnight.toDate(),
                building.getId(),
                MachineUse.ACCREDITED_RESULT.toArray(new MachineUseResult[0])
            )
            .count();

        Double priceCustomer = building.getRate().getPriceCustomer(yesterdayAtMidnight.toDate());

        Double totalInSales = machineUseCount * priceCustomer;

        String body = views.html.reporting.weeklyReport
            .render(
                DateHelper.printDate(sevenDaysAgoAtNoon),
                DateHelper.printDate(yesterdayAtMidnight),
                NumberHelper.formatDoubleWithSeparators(totalInSales)
            )
            .body();

        try {
            EmailService.sendAsync(
                schedule.getReportingEmail(),
                "Informe de ventas – Cierre semanal",
                body
            );
        } catch (Exception e) {
            loggerError(
                "Error while sending weekly report email for building {}: ",
                building.getId(),
                e.getMessage()
            );
        }
    }

    public void doMonthlyReport(Building building, BuildingReportingSchedule schedule) {
        DateTime firstDayOfMonthAtNoon = DateTime
            .now()
            .minusMonths(1)
            .withDayOfMonth(1)
            .withTime(0, 0, 0, 0);
        DateTime yesterdayAtMidnight = DateTime
            .now()
            .minusMonths(1)
            .dayOfMonth()
            .withMaximumValue()
            .withTime(23, 59, 59, 0);

        long machineUseCount = new MachineUseQuery()
            .filterForBillingByBuildingId(
                firstDayOfMonthAtNoon.toDate(),
                yesterdayAtMidnight.toDate(),
                building.getId(),
                MachineUse.ACCREDITED_RESULT.toArray(new MachineUseResult[0])
            )
            .count();

        Double priceCustomer = building.getRate().getPriceCustomer(yesterdayAtMidnight.toDate());

        Double totalInSales = machineUseCount * priceCustomer;

        String body = views.html.reporting.monthlyReport
            .render(
                getMonthName(yesterdayAtMidnight.monthOfYear().get()),
                String.valueOf(yesterdayAtMidnight.getYear()),
                NumberHelper.formatDoubleWithSeparators(totalInSales)
            )
            .body();

        try {
            String subject = "Informe de ventas - Cierre mensual";

            if (schedule.hasCustomReporting()) {
                EmailService.sendAsync(
                    schedule.getReportingEmail(),
                    subject,
                    body,
                    getCustomMonthlyReport(
                        building,
                        schedule,
                        firstDayOfMonthAtNoon,
                        yesterdayAtMidnight
                    ),
                    "Reporte mensual " + getMonthName(yesterdayAtMidnight.monthOfYear().get()),
                    EmailService.AttachExtension.xlsx
                );
            } else {
                EmailService.sendAsync(schedule.getReportingEmail(), subject, body);
            }
        } catch (Exception e) {
            loggerError(
                "Error while sending monthly report email for building {}: ",
                building.getId(),
                e.getMessage()
            );
        }
    }

    public boolean canDoWeeklyReport() {
        return DateTime.now().dayOfWeek().get() == DateTimeConstants.MONDAY;
    }

    public boolean canDoMonthlyReport() {
        return DateTime.now().dayOfMonth().get() == 1;
    }

    private byte[] getCustomMonthlyReport(
        Building building,
        BuildingReportingSchedule schedule,
        DateTime firstDayOfMonthAtNoon,
        DateTime yesterdayAtMidnight
    ) {
        List<Object[]> daySalesObjects = JPA
            .em()
            .createNativeQuery(
                "WITH RECURSIVE all_days AS ( " +
                "    SELECT DATE(:from) AS date " +
                "    UNION ALL " +
                "    SELECT DATE_ADD(date, INTERVAL 1 DAY) " +
                "    FROM all_days " +
                "    WHERE DATE_ADD(date, INTERVAL 1 DAY) <= DATE(:to) " +
                ") " +
                "SELECT " +
                "    DAYOFMONTH(ad.date) AS day, " +
                "    COUNT(mu.id) AS daySales " +
                "FROM all_days ad " +
                "LEFT JOIN machine_use mu " +
                "    ON DATE(mu.timestamp) = ad.date " +
                "    AND mu.result IN " +
                SQLHelper.getAccreditedMachineUseResultCodesForSql() +
                " " +
                "    AND mu.building_id = :buildingId " +
                "LEFT JOIN part c ON mu.card_id = c.id " +
                "LEFT JOIN bill b ON mu.bill_id = b.id " +
                "WHERE (c.id IS NULL OR c.master IS FALSE) " +
                "AND (b.id IS NULL " +
                "     OR (b.bill_type = 'CREDITNOTE' " +
                "     OR b.credit_note = 1)) " +
                "GROUP BY ad.date " +
                "ORDER BY ad.date;"
            )
            .setParameter("buildingId", building.getId())
            .setParameter("from", DateHelper.dateAndTimeDashFormatter.print(firstDayOfMonthAtNoon))
            .setParameter("to", DateHelper.dateAndTimeDashFormatter.print(yesterdayAtMidnight))
            .getResultList();

        Double priceCustomer = building.getRate().getPriceCustomer(yesterdayAtMidnight.toDate());

        ArrayList<Double> monthlySales = new ArrayList<>();
        for (Object[] row : daySalesObjects) {
            Double daySales = ((Number) row[1]).doubleValue();
            monthlySales.add(daySales * priceCustomer);
        }

        ReportBuilder builder = getReportBuilder(schedule);
        return builder.processTemplate(
            schedule.getCustomReportingFilePath(),
            printMonth(firstDayOfMonthAtNoon),
            monthlySales
        );
    }

    private ReportBuilder getReportBuilder(BuildingReportingSchedule schedule) {
        if (
            PuntaShoppingMonthlyReportBuilder.class.getSimpleName()
                .equals(schedule.getCustomReportingBuilder())
        ) {
            return new PuntaShoppingMonthlyReportBuilder();
        }

        return null;
    }
}
