package domains.reporting.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import controllers.AbstractController;
import domains.reporting.services.ReportingLaundromatsService;
import global.APIException;
import global.ErrorMessage;
import global.PermissionValidator;
import java.util.List;
import java.util.stream.Collectors;
import models.Building;
import models.BuildingReportingSchedule;
import models.Role;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import queries.buildings.BuildingQuery;
import security.v1.Secured;

@ErrorMessage
@global.LoggingMessage
@Tx
@Security.Authenticated(Secured.class)
public class ReportingLaundromatsController extends AbstractController {

    public F.Promise<Result> doReports() throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                ReportingLaundromatsService service = new ReportingLaundromatsService();

                List<Building> laundromats = new BuildingQuery().filterLaundromats().find();
                for (Building building : laundromats) {
                    List<BuildingReportingSchedule> schedules = building
                        .getBuildingSetting()
                        .getBuildingReportingSchedules()
                        .stream()
                        .filter(BuildingReportingSchedule::isEnabled)
                        .collect(Collectors.toList());

                    for (BuildingReportingSchedule schedule : schedules) {
                        switch (schedule.getReportingSchedule()) {
                            case DAILY:
                                service.doDailyReport(building, schedule);
                                break;
                            case WEEKLY:
                                if (service.canDoWeeklyReport()) {
                                    service.doWeeklyReport(building, schedule);
                                }
                                break;
                            case MONTHLY:
                                if (service.canDoMonthlyReport()) {
                                    service.doMonthlyReport(building, schedule);
                                }
                                break;
                        }
                    }
                }

                return F.Promise.pure(ok());
            },
            Role.TASK_RUNNER
        );
    }
}
