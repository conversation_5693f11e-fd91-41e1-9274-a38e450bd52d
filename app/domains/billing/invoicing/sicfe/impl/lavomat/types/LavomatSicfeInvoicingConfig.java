package domains.billing.invoicing.sicfe.impl.lavomat.types;

import static utils.ApplicationConfiguration.*;

import domains.billing.invoicing.sicfe.types.SicfeInvoicingServiceConfig;

public class LavomatSicfeInvoicingConfig extends SicfeInvoicingServiceConfig {

    public LavomatSicfeInvoicingConfig() {
        super(
            getSicfeLavomatUrl(),
            getSicfeLavomatEnv(),
            getSicfeLavomatUser(),
            getSicfeLavomatPass(),
            getSicfeLavomatTenant(),
            getSicfeLavomatRuc(),
            getSicfeLavomatCompanyName(),
            getSicfeLavomatCommercialName(),
            getSicfeLavomatPhone(),
            getSicfeLavomatEmail()
        );
    }
}
