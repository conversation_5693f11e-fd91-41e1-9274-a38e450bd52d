package domains.billing;

import domains.billing.exceptions.BillingException;
import global.APIException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import models.*;
import queries.buildings.BuildingQuery;
import queries.untis.UnitQuery;

public class CancellationService extends BillingService {

    public enum CancellationType {
        TOTAL,
        PARTIAL,
    }

    private final Bill bill;
    private final Bill cancelled;
    private final CancellationType type;

    public CancellationService(Bill bill, String reason, CancellationType type) {
        this.bill = bill;
        this.type = type;

        logger("Cancelling bill - bill id: {} - type: {}", bill.getId(), type);

        if (bill.isCreditNote()) {
            this.cancelled = bill.generateDebitNote(reason);

            Bill billReference = Bill.findById(this.bill.getBillReference().getId());
            updateBillState(billReference, Bill.BillState.SENT);
        } else {
            this.cancelled = bill.generateCreditNote(reason, isTotal());
        }
    }

    /**
     * Cancel the bill. Only available for TOTAL cancellations
     * @throws UnsupportedOperationException if the CancellationType is different from TOTAL
     */
    public void cancel() throws BillingException, APIException {
        if (isPartial()) {
            throw new UnsupportedOperationException("Only available for TOTAL cancellations");
        }

        cancel(new ArrayList<>());
    }

    public void cancel(List<BillItem> discreditedItems) throws BillingException, APIException {
        List<MachineUse> allUses = MachineUse.usesOfBill(bill);

        if (bill.belongsToBuilding()) {
            logger("Bill belongs to a Building");

            Building building = new BuildingQuery().get(bill.getBillTo());
            if (building == null) {
                throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
            }

            if (isTotal()) {
                cancelTotally(allUses);
            } else if (isPartial()) {
                cancelPartially(building, discreditedItems, allUses);
            }
        } else if (bill.belongsToUnit()) {
            logger("Bill belongs to an Unit");

            Unit unit = new UnitQuery().get(bill.getBillTo());
            if (unit == null) {
                throw APIException.raise(APIException.APIErrors.UNIT_NOT_FOUND);
            }

            if (isTotal()) {
                cancelTotally(allUses);
            } else if (isPartial()) {
                cancelPartially(unit.getBuilding(), discreditedItems, allUses);
            }
        } else if (bill.belongsToAnonymous()) {
            logger("Bill does not have an owner");

            if (isTotal()) {
                cancelTotally();
            } else if (isPartial()) {
                cancelPartially(discreditedItems);
            }
        } else {
            loggerError(
                "The cancel operation cannot be performed for the bill - id: {} - billToType: {}",
                bill.getId(),
                bill.getBillToType()
            );

            throw new UnsupportedOperationException(
                "The cancel operation cannot be performed for a billToType set as: " +
                bill.getBillToType()
            );
        }

        logger("Bill cancelled.");
    }

    /**
     * Cancel a bill partially.
     * It takes into account the uses already discredited (ignores if they are included on
     * the {@code discreditedItems}) and the provided list of {@code discreditedItems} (it
     * could include cards assignments or balance increases).
     */
    private void cancelPartially(
        Building building,
        List<BillItem> itemsDiscredited,
        List<MachineUse> uses
    ) throws BillingException {
        try {
            logger("Cancelling partially");

            boolean billHasUses = false;

            if (!itemsDiscredited.isEmpty()) {
                billHasUses = itemsDiscredited.stream().anyMatch(BillItem::belongsToUse);
                // items which belongs to card or balance
                itemsDiscredited =
                    itemsDiscredited
                        .stream()
                        .filter(billItem ->
                            !billItem.belongsToUse() && billItem.getDiscreditedAmount() > 0
                        )
                        .collect(Collectors.toList());
            }

            // uses previously refunded
            List<MachineUse> usesDiscredited = new ArrayList<>();
            if (billHasUses && !uses.isEmpty()) {
                usesDiscredited =
                    uses.stream().filter(x -> !x.isAccredited()).collect(Collectors.toList());
            }

            List<MachineUse> usesDiscreditedUnderMinimum = new ArrayList<>();
            List<MachineUse> usesDiscreditedCorrect = new ArrayList<>();
            if (building != null) {
                int usesAllowedToBeDiscredited = getUsesNotAccreditedAboveMinimum(
                    uses.size(),
                    usesDiscredited.size(),
                    building,
                    bill.getBilledPeriodStart()
                );

                // define how many uses are going to be included in the cancellation
                if (usesDiscredited.size() >= usesAllowedToBeDiscredited) {
                    for (int i = 0; i < usesDiscredited.size(); i++) {
                        if (i < usesAllowedToBeDiscredited) {
                            usesDiscreditedCorrect.add(usesDiscredited.get(i));
                        } else {
                            usesDiscreditedUnderMinimum.add(usesDiscredited.get(i));
                        }
                    }
                }
            }

            addItemDiscreditedToPartiallyCancelledBill(itemsDiscredited);

            if (usesDiscreditedUnderMinimum.isEmpty()) {
                addUsesDiscreditedToPartiallyCancelledBill(usesDiscredited);
                // then all uses will be discredited
                cancelled.setTotal(itemsDiscredited, usesDiscredited);
            } else {
                addUsesDiscreditedToPartiallyCancelledBill(usesDiscreditedCorrect);
                // then only some uses will be discredited which are above the minimum
                cancelled.setTotal(itemsDiscredited, usesDiscreditedCorrect);
            }

            cancelled.save();

            if (!usesDiscreditedCorrect.isEmpty() || !itemsDiscredited.isEmpty()) {
                // means:
                // - there are uses to be refunded OR
                // - there are items which belong to card or balance
                this.publishBill(cancelled);

                updateBillState(bill, Bill.BillState.CANCELLED);
            } else {
                // then there is no necessary to publish the bill because it is below the building minimum of usages
                updateBillState(cancelled, Bill.BillState.BELOW_MINIMUM);
            }

            updateBillState(bill, Bill.BillState.CANCELLED);

            // updating the association between uses and bills
            if (billHasUses) {
                cancelMachineUses(usesDiscredited);
            }
        } catch (Exception e) {
            throw new BillingException("Something went wrong while cancel partially", e);
        }
    }

    private void addItemDiscreditedToPartiallyCancelledBill(List<BillItem> itemsDiscredited) {
        List<BillItem> newBillItems = itemsDiscredited
            .stream()
            .map(billItem -> {
                BillItem newBillItem = billItem.copy();
                newBillItem.setBill(cancelled);

                return newBillItem;
            })
            .collect(Collectors.toList());

        cancelled.getDetail().addAll(newBillItems);
    }

    private void addUsesDiscreditedToPartiallyCancelledBill(List<MachineUse> uses) {
        List<BillItem> newBillItems = uses
            .stream()
            .map(machineUse -> {
                BillItem newBillItem = new BillItem(machineUse);
                newBillItem.setBill(cancelled);

                return newBillItem;
            })
            .collect(Collectors.toList());

        cancelled.getDetail().addAll(newBillItems);
    }

    private void cancelPartially(List<BillItem> discreditedItems) throws BillingException {
        cancelPartially(null, discreditedItems, new ArrayList<>());
    }

    /**
     * Cancel a bill of a building totally.
     */
    private void cancelTotally(List<MachineUse> uses) throws BillingException {
        try {
            logger("Cancelling totally");

            cancelled.save();

            this.publishBill(cancelled);

            updateBillState(bill, Bill.BillState.CANCELLED);

            cancelMachineUses(uses);
        } catch (Exception e) {
            loggerError("Couldn't cancel totally.", e);
            throw new BillingException("Couldn't cancel totally.", e);
        }
    }

    private void cancelTotally() throws BillingException {
        cancelTotally(new ArrayList<>());
    }

    /**
     * Get how many uses can be discredited taking into account the minimum of the uses required by the building
     * @param total all uses of the period
     * @param notAccredited uses refunded
     */
    private int getUsesNotAccreditedAboveMinimum(
        int total,
        int notAccredited,
        Building building,
        Date dateToRate
    ) {
        int minimum = building.adjustToBuildingMinimumUses(dateToRate);

        if (total <= minimum) {
            return 0;
        }

        int maxToDiscredit = total - minimum;
        int diff = notAccredited - maxToDiscredit;
        if (diff <= 0) {
            return notAccredited;
        }

        return maxToDiscredit;
    }

    private void cancelMachineUses(List<MachineUse> uses) {
        for (int i = 0; i < uses.size(); i++) {
            MachineUse machineUse = uses.get(i);

            machineUse.setBill(cancelled);
            machineUse.update();
        }
    }

    private boolean isTotal() {
        return CancellationType.TOTAL == type;
    }

    private boolean isPartial() {
        return CancellationType.PARTIAL == type;
    }
}
