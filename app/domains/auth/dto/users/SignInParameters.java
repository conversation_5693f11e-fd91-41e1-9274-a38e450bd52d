package domains.auth.dto.users;

import com.fasterxml.jackson.databind.JsonNode;
import global.APIException;
import global.exceptions.accounts.AccountNotFoundException;
import global.exceptions.users.InvalidCredentialsException;
import models.Account;
import models.User;
import org.apache.commons.lang3.StringUtils;
import play.i18n.Messages;

public class SignInParameters extends dto.JsonBodyActionParameters {

    protected static final String EMAIL_PARAM = "emailAddress";
    protected static final String PASSWORD_PARAM = "password";

    protected String email;
    protected String password;
    protected User user;

    public SignInParameters(JsonNode body) throws APIException {
        this.email = safeString(EMAIL_PARAM, body, StringUtils.EMPTY);
        this.password = safeString(PASSWORD_PARAM, body, StringUtils.EMPTY);
    }

    @Override
    public SignInParameters validate() throws APIException {
        if (StringUtils.isEmpty(this.email) || StringUtils.isEmpty(this.password)) {
            throw APIException.raise(APIException.APIErrors.MISSING_PARAMETERS);
        }

        this.user = User.findByEmailAddress(this.email);

        if (this.user == null || !this.user.doesPasswordMatch(this.password)) {
            throw new InvalidCredentialsException()
                .setDetailMessage(Messages.get("invalid_credentials"));
        }

        Account account = this.user.getMasterAccount();
        if (account == null) {
            throw new AccountNotFoundException();
        }

        if (account.getValidated() == null || !account.getValidated()) {
            throw APIException
                .raise(APIException.APIErrors.UNAUTHORIZED)
                .setDetailMessage("Account needs validation");
        }

        return this;
    }

    public User getUser() {
        return this.user;
    }
}
