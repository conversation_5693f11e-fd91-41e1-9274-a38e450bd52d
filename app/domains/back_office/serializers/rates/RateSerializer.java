package domains.back_office.serializers.rates;

import global.APIException;
import global.APIException.APIErrors;
import java.util.Date;
import java.util.List;
import models.Rate;
import models.RateEvent;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import utils.DateHelper;

public class RateSerializer {

    public static JSONObject listToJson(List<Rate> rates, int level) throws APIException {
        JSONObject ratesJson = new JSONObject();

        JSONArray ratesListJson = new JSONArray();
        for (Rate rate : rates) {
            ratesListJson.put(itemToJson(rate, level));
        }

        try {
            ratesJson.put("rates", ratesListJson);
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing rate list : " + e.getMessage());
        }

        return ratesJson;
    }

    public static JSONObject itemToJson(Rate rate, int level) throws APIException {
        JSONObject rateJson = new JSONObject();

        try {
            rateJson.put("id", rate.getId());
            rateJson.put("name", rate.getName());

            if (level >= 1) {
                rateJson.put("priceCustomer", rate.getPriceCustomer());
                rateJson.put("rateType", rate.getRateType());
                rateJson.put("appliesIVA", rate.appliesIVA());
                rateJson.put("descriptiveMessage", rate.getDescriptiveMessage());
                rateJson.put("minUsesPerWasher", rate.getMinUsesPerWasher());
                rateJson.put("minUsesPerUnit", rate.getMinUsesPerUnit());
                rateJson.put("priceCompany", rate.getPriceCompany());
                rateJson.put("priceM3", rate.getPriceM3());
                rateJson.put("priceKWh", rate.getPriceKWh());
                rateJson.put("priceCardReplacement", rate.getPriceCardReplacement());

                Date validFrom = rate.getValidFrom();
                rateJson.put(
                    "validFrom",
                    validFrom != null
                        ? DateHelper.setStartOfTheDay(validFrom).getTime()
                        : JSONObject.NULL
                );

                rateJson.put(
                    "validFromPretty",
                    validFrom != null
                        ? DateHelper.printDate(new DateTime(validFrom))
                        : JSONObject.NULL
                );

                Date validUntil = rate.getValidUntil();
                rateJson.put(
                    "validUntil",
                    validUntil != null
                        ? DateHelper.setStartOfTheDay(validUntil).getTime()
                        : JSONObject.NULL
                );

                rateJson.put(
                    "validUntilPretty",
                    validFrom != null
                        ? DateHelper.printDate(new DateTime(validUntil))
                        : JSONObject.NULL
                );

                Date lastExpiration = rate.getLastExpiration();
                rateJson.put(
                    "lastExpiration",
                    lastExpiration != null
                        ? DateHelper.setStartOfTheDay(lastExpiration).getTime()
                        : JSONObject.NULL
                );

                RateEvent nextRateEvent = rate.getNextRateEvent();
                rateJson.put(
                    "canNotifyAdmin",
                    nextRateEvent != null && !nextRateEvent.isNotifiedToBuildingAdmin()
                );
            }
        } catch (JSONException e) {
            e.printStackTrace();
            throw APIException
                .raise(APIErrors.BAD_JSON)
                .setDetailMessage("Error serializing rate : " + e.getMessage());
        }

        return rateJson;
    }
}
