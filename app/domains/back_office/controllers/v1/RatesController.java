package domains.back_office.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.back_office.dto.rates.CreateParameters;
import domains.back_office.dto.rates.UpdateParameters;
import domains.back_office.serializers.rates.RateSerializer;
import global.APIException;
import global.BackOfficeCop;
import models.Rate;
import play.libs.F;
import play.mvc.Result;
import policies.actions.Policy;
import policies.rates.RatePolicy;
import queries.rates.RateQuery;
import services.rate.RateFactory;

@Policy(RatePolicy.class)
public class RatesController extends BackOfficeBaseController {

    @Tx(readOnly = true)
    public F.Promise<Result> list() throws APIException {
        RatePolicy allowedPolicy = this.getAllowedPolicy();

        RateQuery query = allowedPolicy.list();

        return json(RateSerializer.listToJson(query.find(), queryLevel(1)));
    }

    @Tx
    @BackOfficeCop
    public F.Promise<Result> create() throws APIException {
        RatePolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.creation();

        CreateParameters params = new CreateParameters(body()).validate();
        RateFactory factory = new RateFactory(params.getRate());
        factory.create();

        return F.Promise.pure(created());
    }

    @Tx
    @BackOfficeCop
    public F.Promise<Result> update(final int id) throws APIException {
        RatePolicy allowedPolicy = this.getAllowedPolicy();
        RateQuery query = allowedPolicy.update(id);
        Rate rate = query.single();

        UpdateParameters params = new UpdateParameters(rate, body()).validate();

        RateFactory factory = new RateFactory(rate);
        factory.update(params);

        return F.Promise.pure(ok());
    }
}
