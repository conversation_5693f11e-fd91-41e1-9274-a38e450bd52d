package domains.sale_notifier.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import controllers.AbstractController;
import domains.sale_notifier.exceptions.CardIsMasterException;
import domains.sale_notifier.exceptions.SaleAlreadyNotifiedException;
import domains.sale_notifier.exceptions.SaleAlreadyRefundedException;
import domains.sale_notifier.exceptions.SaleNotNotifiedException;
import domains.sale_notifier.services.SaleNotifierService;
import domains.sale_notifier.services.wrappers.SaleWrapper;
import global.APIException;
import global.ErrorMessage;
import global.PermissionValidator;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import models.Bill;
import models.ExternalSaleNotificationRecord;
import models.MachineUse;
import models.Role;
import org.joda.time.DateTime;
import play.libs.F;
import play.mvc.Result;
import play.mvc.Security;
import queries.bills.BillToNotifyQuery;
import queries.machine_uses.MachineUseToNotifyQuery;
import security.v1.Secured;

@ErrorMessage
@global.LoggingMessage
@Tx
@Security.Authenticated(Secured.class)
public class SaleNotifierController extends AbstractController {

    private static final BillToNotifyQuery billToNotifyQuery = new BillToNotifyQuery();
    private static final MachineUseToNotifyQuery machineUseToNotifyQuery = new MachineUseToNotifyQuery();

    public F.Promise<Result> notifyBillSale(int buildingId, int billId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                Bill bill = billToNotifyQuery.getBillToNotify(buildingId, billId);
                if (bill == null) {
                    throw APIException.raise(APIException.APIErrors.BILL_NOT_FOUND);
                }

                // Bill already notified. Must refund before next notification
                ExternalSaleNotificationRecord lastNotificationRecord = bill.getLastExternalSaleNotificationRecord();
                if (lastNotificationRecord != null && !lastNotificationRecord.isRefunded()) {
                    play.Logger.error(
                        "Bill {} already notified at {}, external id: {}",
                        bill.getId(),
                        lastNotificationRecord.getExternalId()
                    );
                    throw new SaleAlreadyNotifiedException();
                }

                List<Bill> bills = new ArrayList<>();
                bills.add(bill);

                List<SaleWrapper> salesToNotify = bills
                    .stream()
                    .map(SaleWrapper::new)
                    .collect(Collectors.toList());
                SaleNotifierService.getNotificationService(buildingId, salesToNotify).notifySale();

                return F.Promise.<Result>pure(noContent());
            },
            Role.MASTER
        );
    }

    public F.Promise<Result> notifyMachineUseSale(int buildingId, int useId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                MachineUse use = machineUseToNotifyQuery.getMachineUseToNotify(buildingId, useId);
                if (use == null) {
                    throw APIException.raise(APIException.APIErrors.MACHINE_USE_NOT_FOUND);
                }

                // MASTER cards should not be notified
                if (use.getCard() != null && use.getCard().isMaster()) {
                    play.Logger.error("Use {} is from MASTER card.", use.getId());
                    throw new CardIsMasterException();
                }

                // Use already notified. Must refund before next notification
                ExternalSaleNotificationRecord lastNotificationRecord = use.getLastExternalSaleNotificationRecord();
                if (lastNotificationRecord != null && !lastNotificationRecord.isRefunded()) {
                    play.Logger.error(
                        "Use {} already notified at {}, external id: {}",
                        use.getId(),
                        lastNotificationRecord.getExternalId()
                    );
                    throw new SaleAlreadyNotifiedException();
                }

                List<MachineUse> uses = new ArrayList<>();
                uses.add(use);

                List<SaleWrapper> salesToNotify = uses
                    .stream()
                    .map(SaleWrapper::new)
                    .collect(Collectors.toList());
                SaleNotifierService.getNotificationService(buildingId, salesToNotify).notifySale();

                return F.Promise.<Result>pure(noContent());
            },
            Role.MASTER
        );
    }

    public F.Promise<Result> bulkNotifySales(int buildingId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                // timezone corrected
                DateTime from = DateTime.now().minusHours(4).minusMinutes(30);
                DateTime to = DateTime.now().minusHours(3);

                play.Logger.debug("Starting bulk sale notification from: {} to: {}", from, to);

                List<Bill> billsToNotify = new BillToNotifyQuery()
                    .getBillsToNotify(buildingId, from, to);
                play.Logger.debug("Got {} bills to notify", billsToNotify.size());

                List<MachineUse> usesToNotify = new MachineUseToNotifyQuery()
                    .getMachineUsesToNotify(buildingId, from, to);
                play.Logger.debug("Got {} uses to notify", usesToNotify.size());

                List<SaleWrapper> billSalesToNotify = billsToNotify
                    .stream()
                    .map(SaleWrapper::new)
                    .collect(Collectors.toList());
                List<SaleWrapper> useSalesToNotify = usesToNotify
                    .stream()
                    .map(SaleWrapper::new)
                    .collect(Collectors.toList());
                List<SaleWrapper> salesToNotify = Stream
                    .concat(billSalesToNotify.stream(), useSalesToNotify.stream())
                    .collect(Collectors.toList());

                if (!salesToNotify.isEmpty()) {
                    play.Logger.debug("Sending {} sales to notify", salesToNotify.size());
                    SaleNotifierService
                        .getNotificationService(buildingId, salesToNotify)
                        .notifySale();
                }

                return F.Promise.<Result>pure(noContent());
            },
            Role.TASK_RUNNER
        );
    }

    public F.Promise<Result> notifyBillRefund(int buildingId, int billId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                Bill bill = billToNotifyQuery.getBillToNotify(buildingId, billId);
                if (bill == null) {
                    throw APIException.raise(APIException.APIErrors.BILL_NOT_FOUND);
                }

                // Use already notified. Must refund before next notification
                ExternalSaleNotificationRecord lastNotificationRecord = bill.getLastExternalSaleNotificationRecord();

                if (lastNotificationRecord == null) {
                    play.Logger.error("Bill {} is not notified", bill.getId());

                    throw new SaleNotNotifiedException();
                }

                if (lastNotificationRecord.isRefunded()) {
                    play.Logger.error(
                        "Bill {} already refunded at {}, external id: {}",
                        bill.getId(),
                        lastNotificationRecord.getRefundedAt(),
                        lastNotificationRecord.getExternalId()
                    );

                    throw new SaleAlreadyRefundedException();
                }

                List<Bill> billsToRefund = new ArrayList<>();
                billsToRefund.add(bill);

                List<SaleWrapper> salesToRefund = billsToRefund
                    .stream()
                    .map(SaleWrapper::new)
                    .collect(Collectors.toList());

                SaleNotifierService
                    .getNotificationService(buildingId, salesToRefund)
                    .notifyRefund();

                return F.Promise.<Result>pure(noContent());
            },
            Role.MASTER
        );
    }

    public F.Promise<Result> notifyUseRefund(int buildingId, int useId) throws APIException {
        return PermissionValidator.runIfHasRole(
            ctx(),
            () -> {
                MachineUse use = machineUseToNotifyQuery.getMachineUseToNotify(buildingId, useId);
                if (use == null) {
                    throw APIException.raise(APIException.APIErrors.MACHINE_USE_NOT_FOUND);
                }

                // MASTER cards should not be notified
                if (use.getCard().isMaster()) {
                    play.Logger.error("Use {} is from MASTER card.", use.getId());
                    throw new CardIsMasterException();
                }

                // Use already notified. Must refund before next notification
                ExternalSaleNotificationRecord lastNotificationRecord = use.getLastExternalSaleNotificationRecord();
                if (lastNotificationRecord == null) {
                    play.Logger.error("Use {} is not notified", use.getId());

                    throw new SaleNotNotifiedException();
                }

                if (lastNotificationRecord.isRefunded()) {
                    play.Logger.error(
                        "Use {} already refunded at {}, external id: {}",
                        use.getId(),
                        lastNotificationRecord.getRefundedAt(),
                        lastNotificationRecord.getExternalId()
                    );

                    throw new SaleAlreadyRefundedException();
                }

                List<MachineUse> usesToRefund = new ArrayList<>();
                usesToRefund.add(use);

                List<SaleWrapper> salesToRefund = usesToRefund
                    .stream()
                    .map(SaleWrapper::new)
                    .collect(Collectors.toList());

                SaleNotifierService
                    .getNotificationService(buildingId, salesToRefund)
                    .notifyRefund();

                return F.Promise.<Result>pure(noContent());
            },
            Role.MASTER
        );
    }
}
