package domains.activations;

import domains.activations.internal.Factory;
import global.APIException;
import models.*;

public interface MachineActivationService {
    /**
     * Creates a new instance of expected service.
     */
    static MachineActivationService instance() {
        return Factory.create();
    }

    /**
     * Set the machine to be activated.
     */
    MachineActivationService forMachine(String serialNumber);

    /**
     * Set the machine to be activated.
     */
    MachineActivationService forMachine(Machine machine);

    /**
     * Set the card to be used to activate the machine.
     */
    MachineActivationService usingCard(String uid);

    /**
     * Set the card to be used to activate the machine.
     */
    MachineActivationService usingCard(Card card);

    /**
     * Set the building where the activation is performed.
     */
    MachineActivationService atBuilding(int buildingId);

    /**
     * Set the building where the activation is performed.
     */
    MachineActivationService atBuilding(Building building);

    /**
     * Set the channel through which the activation is performed.
     */
    MachineActivationService viaChannel(String channel);

    default MachineActivationService viaAssistant() {
        return viaChannel(Channel.ASSISTANT_CHANNEL);
    }

    default MachineActivationService viaMobileApp() {
        return viaChannel(Channel.APPLICATION_CHANNEL);
    }

    default MachineActivationService viaQR() {
        return viaChannel(Channel.QR_CHANNEL);
    }

    default MachineActivationService viaRPI() {
        return viaChannel(Channel.RPI_CHANNEL);
    }

    default MachineActivationService viaTotem() {
        return viaChannel(Channel.TOTEM_CHANNEL);
    }

    default MachineActivationService viaWhatsApp() {
        return viaChannel(Channel.WHATSAPP_CHANNEL);
    }

    /**
     * Indicates if the activation has to send a notification to the machine.
     */
    MachineActivationService sendingMqttMessage();

    /**
     * Indicates if the activation has to send a notification to the machine.
     */
    MachineActivationService sendingMqttMessage(boolean sendMqtt);

    /**
     * Set who initiated the activation.
     * See ActivationParameters#activator for more details.
     */
    MachineActivationService initiatedBy(User activator);

    /**
     * Set which user initiated the activation.
     * See ActivationParameters#user for more details.
     */
    MachineActivationService impersonates(String email);

    /**
     * Indicated if the user belongs to a group.
     */
    MachineActivationService forGroup(int groupId);

    /**
     * Updates users' expo device token.
     */
    MachineActivationService notifyDevice(String token);

    /**
     * Set the transaction which originated the activation.
     */
    MachineActivationService throughTransaction(Integer transactionId);

    /**
     * Set the transaction which originated the activation.
     */
    MachineActivationService throughTransaction(Transaction transaction);

    /**
     * Set the transaction which originated the activation.
     */
    MachineActivationService throughTransaction(String transactionPublicId);

    /**
     * Main process to activate a machine.
     */
    ActivationResult activate() throws APIException;

    /**
     * Returns if someone can initialize an activation purchase for the machine
     */
    boolean canActivateMachine(Machine machine, String channel, User activator) throws APIException;

    /**
     * Returns if someone can initialize an activation purchase for the machine without authenticate
     */
    boolean canActivateMachineByAnonymous(Machine machine, String channel) throws APIException;
    // TODO: replace canActivateMachine methods with a single one:
    //   instance().forMachine(..).viaChannel(..).canActivateMachine()
}
