package domains.exchange_rate.services.bcu.soap.generated;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>Java class for respuestastatus complex type.
 *
 * <p>The following schema fragment specifies the expected content contained within this class.
 *
 * <pre>
 * &lt;complexType name="respuestastatus">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}byte"/>
 *         &lt;element name="codigoerror" type="{http://www.w3.org/2001/XMLSchema}short"/>
 *         &lt;element name="mensaje" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "respuestastatus", propOrder = { "status", "codigoerror", "mensaje" })
public class Respuestastatus {

    protected byte status;
    protected short codigoerror;

    @XmlElement(required = true)
    protected String mensaje;

    /**
     * Gets the value of the status property.
     *
     */
    public byte getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     *
     */
    public void setStatus(byte value) {
        this.status = value;
    }

    /**
     * Gets the value of the codigoerror property.
     *
     */
    public short getCodigoerror() {
        return codigoerror;
    }

    /**
     * Sets the value of the codigoerror property.
     *
     */
    public void setCodigoerror(short value) {
        this.codigoerror = value;
    }

    /**
     * Gets the value of the mensaje property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getMensaje() {
        return mensaje;
    }

    /**
     * Sets the value of the mensaje property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setMensaje(String value) {
        this.mensaje = value;
    }
}
