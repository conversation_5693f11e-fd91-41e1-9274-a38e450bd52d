package domains.bot.services.administrations;

import models.*;
import services.ContactSupportService;
import utils.email.EmailService;

public class ContactAdministrationService extends services.BaseService {

    public void requestNewCard(
        String email,
        String firstName,
        String lastName,
        String phone,
        Building building,
        Unit unit,
        String unitNumber,
        String tower,
        String server
    ) throws Exception {
        try {
            Administration admin = building.getAdministration();
            String adminEmails = admin.getContact();
            String name = admin.getName();
            CardRequest cardRequest = new CardRequest(
                building,
                firstName,
                lastName,
                phone,
                unit,
                tower,
                email
            );
            cardRequest.setUserUnitInput(unitNumber);
            cardRequest.save();
            String token = cardRequest.getToken();
            String confirmUrl = server + "/api/v1/cardRequest/confirm?token=" + token;
            String rejectUrl = server + "/api/v1/cardRequest/reject?token=" + token;
            String body = domains.bot.views.html.requestNewCard
                .render(
                    name,
                    confirmUrl,
                    rejectUrl,
                    firstName,
                    lastName,
                    email,
                    phone,
                    building.getName(),
                    unitNumber,
                    tower
                )
                .body();

            if (Card.ContractType.PREPAID == building.getContractType() || adminEmails.isEmpty()) {
                EmailService.sendAsync(
                    ContactSupportService.SUPPORT_INFO_EMAIL,
                    "Este mail debe ser reenviado a la administración " + name,
                    body
                );
            } else {
                String to = EmailService.generateRecipientsList(
                    ContactSupportService.SUPPORT_INFO_EMAIL,
                    adminEmails
                );
                EmailService.sendAsync(to, "LAVOMAT - Confirmación de solicitud de tarjeta", body);
            }
        } catch (Exception e) {
            loggerError("Request New card admin mail error");
            e.printStackTrace();
            throw e;
        }
    }

    public void requestCardActivation(
        String uid,
        String email,
        String firstName,
        String lastName,
        String phone,
        Building building,
        Unit unit,
        String tower,
        Card card,
        String server
    ) throws Exception {
        try {
            Administration admin = building.getAdministration();
            String adminEmails = admin.getContact();
            String name = admin.getName();
            CardRequest cardRequest = new CardRequest(
                building,
                firstName,
                lastName,
                phone,
                unit,
                tower,
                email,
                card
            );
            cardRequest.save();
            String token = cardRequest.getToken();
            String acceptUrl = server + "/api/v1/cardRequest/activate?token=" + token;
            String rejectUrl = server + "/api/v1/cardRequest/rejectActivation?token=" + token;
            String body = domains.bot.views.html.requestCardActivation
                .render(
                    name,
                    acceptUrl,
                    rejectUrl,
                    firstName,
                    lastName,
                    email,
                    phone,
                    building.getName(),
                    unit.getNumber(),
                    tower
                )
                .body();

            if (adminEmails.isEmpty()) {
                EmailService.sendAsync(
                    ContactSupportService.SUPPORT_INFO_EMAIL,
                    "Este mail debe ser reenviado a la administración " + name,
                    body
                );
            } else {
                EmailService.sendAsync(
                    adminEmails,
                    "LAVOMAT - Solicitud de activación de tarjeta",
                    body
                );
            }
        } catch (Exception e) {
            loggerError("Request card activation admin mail error");
            e.printStackTrace();
            throw e;
        }
    }
}
