package domains.bot.services.users;

import global.APIException;
import global.APIException.APIErrors;
import java.io.IOException;
import models.Card;
import org.apache.commons.mail.EmailException;
import services.ContactSupportService;
import utils.email.EmailService;

public class UserCardAssignmentService extends services.BaseService {

    public String checkCardAssignment(String uid, String email) throws APIException {
        Card card = Card.findByUID(uid);
        String message = "";
        String subject = "LAVOMAT - Número de tarjeta ingresado inválido";

        if (card == null) {
            message =
                "No tenemos registrada ninguna tarjeta con el número UID: " +
                uid +
                " ingresado en el WP Bot. Por favor verifique el mismo.";
            String body = views.html.generalMessage.render(message, email).body();

            try {
                EmailService.sendAsync(
                    email + ", " + ContactSupportService.SUPPORT_INFO_EMAIL,
                    subject,
                    body
                );
            } catch (EmailException | IOException ex) {
                loggerError(
                    "Checking Card Assignment could not send email: {} - ex: ",
                    email,
                    ex.getMessage()
                );
            }

            throw APIException.raise(APIErrors.CARD_NOT_FOUND);
        }

        if (card.getPrePaidCardholder() == null) {
            message = "La tarjeta no esta asignada al email ingresado.";
        } else if (card.getPrePaidCardholder().getEmailAddress().equals(email)) {
            message = "La tarjeta ya está asignada al email ingresado en el WP Bot.";
        } else {
            message =
                "La tarjeta con el número UID:" +
                uid +
                " ingresado en el WP Bot está asociada a otro usuario.";
            String body = views.html.generalMessage.render(message, email).body();

            try {
                EmailService.sendAsync(
                    email + "," + ContactSupportService.SUPPORT_INFO_EMAIL,
                    subject,
                    body
                );
            } catch (EmailException | IOException ex) {
                loggerError(
                    "Checking Card Assignment could not send email: {} - ex: ",
                    email,
                    ex.getMessage()
                );
            }

            throw APIException.raise(APIErrors.CARD_ALREADY_ASSIGNED_TO_PREPAID_USER);
        }

        return message;
    }
}
