package domains.bot.controllers.v1;

import com.play4jpa.jpa.db.Tx;
import domains.bot.dto.buildings.GetBuildingByLocationParameters;
import domains.bot.serializers.BuildingSerializer;
import domains.bot.services.buildings.SearchNearBuildingsServices;
import global.APIException;
import global.APIException.APIErrors;
import java.util.List;
import models.Building;
import play.libs.F.Promise;
import play.mvc.Result;
import policies.actions.Policy;
import policies.buildings.BuildingPolicy;
import queries.buildings.BuildingQuery;

@Policy(BuildingPolicy.class)
public class BuildingsController extends BotBaseController {

    @Tx(readOnly = true)
    public Promise<Result> getLaundromatDetails() throws APIException {
        BuildingPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        List<Building> buildings = new BuildingQuery().filterLaundromats().find();

        return json(BuildingSerializer.itemToJson(buildings.get(0), level()));
    }

    @Tx(readOnly = true)
    public Promise<Result> getBuildingsDetailsByLocation() throws APIException {
        BuildingPolicy allowedPolicy = this.getAllowedPolicy();
        allowedPolicy.list();

        GetBuildingByLocationParameters params = new GetBuildingByLocationParameters(body())
            .validate();

        List<Building> buildings = SearchNearBuildingsServices.search(
            params.getLatitude(),
            params.getLongitude()
        );

        if (buildings.isEmpty()) {
            throw APIException.raise(APIErrors.BUILDING_NOT_FOUND);
        }

        return json(BuildingSerializer.listToJson(buildings, level()));
    }
}
