package models;

/*
Some considerations when creating a new MachineUseResult:
- Starting number should indicate the type of use
- Ending with '0' relates to POSTPAID uses
- Ending with '1' relates to PREPAID uses
- Ending with '2' relates to the card turned INACTIVE
*/
public enum MachineUseResult {
    /* ===== Activation Results ===== */
    POSTPAID_ACTIVATION("POSPAGO-ACTIVA", 0),
    PREPAID_ACTIVATION_WITH_BALANCE("PREPAGO-ACTIVA-CON-SALDO", 1),
    INACTIVE("INACTIVA", 2),
    // INFO: Missing code (4) is an error
    QR("QR", 5),
    LAUNDROMAT("LAUNDROMAT", 6),
    WHATSAPP_ACTIVATION_WITH_BALANCE("WHATSAPP-ACTIVA-CON-SALDO", 7),
    WHATSAPP_POSTPAID_ACTIVATION("WHATSAPP-POSTPAGO-ACTIVA", 8),
    POSTPAID_NOT_ACCREDITED("POSTPAGO-DESACREDITADO", 10),
    PREPAID_NOT_ACCREDITED("PREPAGO-DESACREDITADO", 11),
    POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITH_BALANCE(
        "POSTPAGO-ACTIVA-USO-PRE-BLOQUEADO-CON-SALDO",
        30
    ),

    /* ===== Status Results ===== */
    IN_PROGRESS("EN PROGRESO", 12),
    PRICE_UNAVAILABLE("PRECIO-NO-DISPONIBLE", 14),
    IN_MAINTENANCE("EN-MANTENIMIENTO", 16),
    MAINTENANCE_INTERRUPTED("MANTENIMIENTO-INTERRUMPIDO", 26),
    OUT_OF_WORKING_HOURS("FUERA-DE-HORARIO", 20),
    BOOKED("RESERVADA", 61), // 6=LAUNDROMAT, 1=PREPAID

    /* ===== Error Results ===== */
    PREPAID_ACTIVATION_WITHOUT_BALANCE("PREPAGO-ACTIVA-SIN-SALDO", 3),
    BUILDING_ERROR("ERROR-EDIFICIO", 4),
    OUT_OF_WOKRING_HOURS("FUERA-DE-HORARIO", 20),
    POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITHOUT_BALANCE(
        "POSTPAGO-ACTIVA-USO-PRE-BLOQUEADO-SIN-SALDO",
        32
    ),
    BOOK_CANCELED("RESERVADA-CANCELADA", 64), // 6=LAUNDROMAT, 4=Error
    // TODO: fix the code number after defining a new criterion for it
    RELAY_ERROR("RELAY-ERROR", 66), // 6=LAUNDROMAT, 6=LAUNDROMAT
    NON_APPLIED("N/A", 99);

    private final String name;
    private final Integer code;

    MachineUseResult(String name, Integer code) {
        this.name = name;
        this.code = code;
    }

    public String getName() {
        return this.name;
    }

    public Integer getCode() {
        return this.code;
    }

    public String getCodeString() {
        return this.code.toString();
    }

    public static MachineUseResult getEnum(String value) {
        for (MachineUseResult result : values()) {
            if (result.code.toString().equals(value)) {
                return result;
            }
        }

        return NON_APPLIED;
    }
}
