package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import javax.persistence.*;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import play.db.jpa.JPA;
import services.bill.CFEItem;

@Entity
@Table(
    indexes = {
        @Index(name = "RESULT", columnList = "result"),
        @Index(name = "TIMESTAMP", columnList = "timestamp"),
        @Index(name = "BUILDING_ID_MACHINE_ID", columnList = "building_id, machine_id"),
        @Index(name = "BUILDING_ID_TIMESTAMP", columnList = "building_id, timestamp"),
    }
)
public class MachineUse extends BaseModel<MachineUse> implements CFEItem, BillableItem {

    private static final long serialVersionUID = -3557047328270703103L;

    public static final List<MachineUseResult> ACCREDITED_RESULT = Arrays.asList(
        MachineUseResult.POSTPAID_ACTIVATION,
        MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE,
        MachineUseResult.QR,
        MachineUseResult.LAUNDROMAT,
        MachineUseResult.WHATSAPP_ACTIVATION_WITH_BALANCE,
        MachineUseResult.POSTPAID_ACTIVATION_PRE_BLOCKED_USE_WITH_BALANCE,
        MachineUseResult.WHATSAPP_POSTPAID_ACTIVATION
    );

    @Id
    @GeneratedValue
    private int id;

    private Date timestamp;

    @OneToOne(cascade = { CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE })
    private Card card;

    @OneToOne(cascade = { CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE })
    private Machine machine;

    @ManyToOne(
        cascade = { CascadeType.PERSIST, CascadeType.REFRESH, CascadeType.MERGE },
        fetch = FetchType.LAZY
    )
    private Building building;

    private String headline;

    private String uid;

    private String result;

    private String channel;

    private double waterConsumption;
    private double energyConsumption;

    /**
     * audit workflow id
     */
    private int transaction_id;

    private String reason;

    @Transient
    private boolean alert;

    @ManyToOne
    private Bill bill;

    @OneToMany(mappedBy = "machineUse")
    private List<ExternalSaleNotificationRecord> externalSaleNotificationRecords;

    private static Finder<Integer, MachineUse> find = new Finder<Integer, MachineUse>(
        Integer.class,
        MachineUse.class
    );

    public MachineUse() {}

    public MachineUse(Date timestamp, Card card, Machine machine) {
        this.timestamp = timestamp;
        this.card = card;
        this.machine = machine;
        this.building = machine.getBuilding();
        this.headline = card.getUuid();
    }

    public static List<MachineUse> findAll() {
        return find.all();
    }

    public static MachineUse findById(int machineUseId) {
        return find.byId(machineUseId);
    }

    public static List<MachineUse> find(
        Date from,
        Date to,
        int buildingId,
        int machineId,
        int unitId,
        String cardUId,
        String keyword
    ) {
        return find(
            from,
            to,
            buildingId,
            machineId,
            unitId,
            cardUId,
            keyword,
            new ArrayList<MachineUseResult>()
        );
    }

    public static List<MachineUse> find(
        Date from,
        Date to,
        int buildingId,
        int machineId,
        int unitId,
        String cardUId,
        String keyword,
        List<MachineUseResult> results
    ) {
        MachineUseResult[] resultsArray = results.stream().toArray(MachineUseResult[]::new);
        return find(from, to, buildingId, machineId, unitId, cardUId, keyword, resultsArray);
    }

    public static List<MachineUse> find(
        Date from,
        Date to,
        int buildingId,
        int machineId,
        int unitId,
        String cardUId,
        String keyword,
        MachineUseResult... results
    ) {
        DefaultQuery<MachineUse> query = (DefaultQuery<MachineUse>) find.query();

        SimpleDateFormat fromSdf = new SimpleDateFormat("yyyy-MM-dd '00:00:00'");
        SimpleDateFormat untilSdf = new SimpleDateFormat("yyyy-MM-dd '23:59:59'");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        play.Logger.debug(
            "Machine Use - find - parameters - from: {} - to: {} - building: {} - machine: {} - unit: {} - card: {}",
            from,
            to,
            buildingId,
            machineId,
            unitId,
            cardUId
        );

        try {
            if (from != null) {
                Date parsedDate = sdf.parse(fromSdf.format(from));
                query.ge("timestamp", parsedDate);
                play.Logger.debug("Machine Use - find - from: {} - parsed: {}", from, parsedDate);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        try {
            if (to != null) {
                Date parsedDate = sdf.parse(untilSdf.format(to));
                query.le("timestamp", parsedDate);
                play.Logger.debug("Machine Use - find - to: {} - parsed: {}", to, parsedDate);
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (machineId > 0) {
            query.join("machine").eq("machine.id", machineId);
        }

        if (buildingId > 0) {
            query
                .getCriteria()
                .createAlias("machine.building", "building")
                .add(Restrictions.eq("building.id", buildingId));
        }

        if (unitId > 0) {
            query
                .getCriteria()
                .createAlias("card.unit", "unit")
                .add(Restrictions.eq("unit.id", unitId));
        }

        if (cardUId != null) {
            query.join("card").eq("card.uuid", cardUId);
        }

        if (results != null && results.length > 0) {
            List<String> resultsCodes = Arrays
                .stream(results)
                .map(x -> x.getCode().toString())
                .collect(Collectors.toList());
            query.getCriteria().add(Restrictions.in("result", resultsCodes));
        }

        return query.findList();
    }

    public static List<MachineUse> findForBillingByCard(
        Date from,
        Date to,
        int cardId,
        int prepaidUses,
        MachineUseResult... results
    ) {
        return findForBilling(from, to, cardId, -1, -1, prepaidUses, results);
    }

    /**
     * Get machine uses which are not associated to a bill or, in case they are,
     * the bill is not a Credit Note.
     */
    public static List<MachineUse> findForBilling(
        Date from,
        Date to,
        int cardId,
        int unitId,
        int buildingId,
        int prepaidUses,
        MachineUseResult... results
    ) {
        DefaultQuery<MachineUse> query = (DefaultQuery<MachineUse>) find.query();

        SimpleDateFormat fromSdf = new SimpleDateFormat("yyyy-MM-dd '00:00:00'");
        SimpleDateFormat untilSdf = new SimpleDateFormat("yyyy-MM-dd '23:59:59'");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        try {
            if (from != null) {
                query.ge("timestamp", sdf.parse(fromSdf.format(from)));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        try {
            if (to != null) {
                query.le("timestamp", sdf.parse(untilSdf.format(to)));
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        if (buildingId > 0) {
            query
                .getCriteria()
                .createAlias("machine.building", "building")
                .add(Restrictions.eq("building.id", buildingId));
        }

        if (unitId > 0) {
            query
                .getCriteria()
                .createAlias("card.unit", "unit")
                .add(Restrictions.eq("unit.id", unitId));
        }

        if (cardId > 0) {
            query.join("card").eq("card.id", cardId);
        }

        if (results != null && results.length > 0) {
            List<String> resultsCodes = Arrays
                .stream(results)
                .map(x -> x.getCode().toString())
                .collect(Collectors.toList());

            query.getCriteria().add(Restrictions.in("result", resultsCodes));
        }

        if (prepaidUses > 0) {
            query.orderByAsc("timestamp");
        } else {
            // Restriction: No bill or credit note bill
            query
                .getCriteria()
                .createAlias("bill", "mu_bill", JoinType.LEFT_OUTER_JOIN)
                .add(
                    Restrictions.or(
                        Restrictions.isNull("mu_bill.id"),
                        Restrictions.or(
                            Restrictions.eq("mu_bill.billType", Bill.BillType.CREDITNOTE),
                            Restrictions.eq("mu_bill.creditNote", true)
                        )
                    )
                );
        }

        return query.findList();
    }

    public static List<MachineUse> usesOfBill(Bill bill) {
        DefaultQuery<MachineUse> q = (DefaultQuery<MachineUse>) find.query();
        q.join("bill").eq("bill.id", bill.getId());
        return q.findList();
    }

    public static List<MachineUse> usesOfBill(int billId) {
        DefaultQuery<MachineUse> q = (DefaultQuery<MachineUse>) find.query();
        q.join("bill").eq("bill.id", billId);
        return q.findList();
    }

    public static MachineUse getLastMachineUseByMachine(Machine machine) {
        return MachineUse.getLastMachineUseByMachine(machine, new ArrayList<MachineUseResult>());
    }

    public static MachineUse getLastMachineAccreditedUseByMachine(Machine machine) {
        return MachineUse.getLastMachineUseByMachine(machine, ACCREDITED_RESULT);
    }

    public static MachineUse getLastMachineUseByMachine(
        Machine machine,
        List<MachineUseResult> results
    ) {
        try {
            DefaultQuery<MachineUse> query = (DefaultQuery<MachineUse>) find.query();
            query.join("machine").eq("machine.id", machine.getId());

            if (results != null && !results.isEmpty()) {
                List<String> resultsCodes = results
                    .stream()
                    .map(x -> x.getCode().toString())
                    .collect(Collectors.toList());

                query.getCriteria().add(Restrictions.in("result", resultsCodes));
            }

            query.orderByDesc("timestamp");
            query.setMaxRows(1);

            List<MachineUse> uses = query.findList();
            if (!uses.isEmpty()) {
                return uses.get(0);
            }
        } catch (Exception e) {}

        return null;
    }

    public static List<MachineUse> getMachineUsesByDateRange(Machine machine, Date startDate) {
        try {
            DefaultQuery<MachineUse> q = (DefaultQuery<MachineUse>) find.query();
            q.join("machine").eq("machine.id", machine.getId());
            if (startDate != null) {
                q.between("timestamp", startDate, new Date());
            }
            q.orderByDesc("timestamp");
            return q.findList();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static Date getFirstMachineUseByBuilding(final int buildingId) {
        try {
            List<String> accreditedResults = MachineUse.ACCREDITED_RESULT
                .stream()
                .map(result -> result.getCode().toString())
                .collect(Collectors.toList());

            TypedQuery<Date> typedQuery = JPA
                .em()
                .createQuery(
                    "SELECT mu.timestamp " +
                    "FROM models.MachineUse mu " +
                    "WHERE mu.building.id = :buildingId " +
                    "AND mu.result IN (:accreditedResults) " +
                    "ORDER BY mu.timestamp ASC",
                    Date.class
                )
                .setMaxResults(1)
                .setParameter("buildingId", buildingId)
                .setParameter("accreditedResults", accreditedResults);

            return typedQuery.getSingleResult();
        } catch (Exception e) {
            e.printStackTrace();
        }

        return null;
    }

    public static boolean existForMachineAfterDate(int machineId, Date date) {
        if (date == null) {
            return false;
        }

        DefaultQuery<MachineUse> query = (DefaultQuery<MachineUse>) find.query();

        query.join("machine").eq("machine.id", machineId);
        query.ge("timestamp", date);
        query.setMaxRows(1);

        return query.findRowCount() > 0;
    }

    public Bill getBill() {
        return this.bill;
    }

    public void setBill(Bill bill) {
        this.bill = bill;
    }

    public int getId() {
        return this.id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Date getTimestamp() {
        return this.timestamp;
    }

    public void setTimestamp(Date timestamp) {
        this.timestamp = timestamp;
    }

    public Card getCard() {
        return this.card;
    }

    public void setCard(Card card) {
        this.card = card;
    }

    public Machine getMachine() {
        return this.machine;
    }

    public void setMachine(Machine machine) {
        this.machine = machine;
    }

    public String getHeadline() {
        return this.headline;
    }

    public void setHeadline(String headline) {
        this.headline = headline;
    }

    public String getUid() {
        return this.uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getTransactionId() {
        return this.transaction_id;
    }

    public void setTransactionId(int tid) {
        this.transaction_id = tid;
    }

    @Override
    @Transient
    public int getIndDet() {
        return getMachine().getBuilding().getRate().appliesIVA() ? 3 : 1;
    }

    @Override
    @Transient
    public String getNomItem() {
        String validPeriod = " ";
        try {
            if (getMachine() != null) validPeriod +=
                "(Aplica Tarifa del Período: " +
                getMachine().getBuilding().getRate().getValidPeriod(this.getTimestamp()) +
                ")"; else validPeriod += "- Ajuste por mínimo";
        } catch (Exception e) {}

        return (
            "Uso " +
            (
                getMachine() != null && getMachine().hasSpecialRate()
                    ? "con Dosif. Jaboón"
                    : "Simple"
            ) +
            validPeriod
        );
    }

    @Override
    @Transient
    public int getCantidad() {
        return 1;
    }

    @Override
    @Transient
    public String getUnidadMedida() {
        return BillItem.MEASURE_UNIT_USE;
    }

    @Override
    @Transient
    public double getPrecioUnitario() {
        return this.machine.getMachineRate().getPriceCompany(this.getTimestamp()) / 1.22;
    }

    public double getPriceCustomer() {
        return this.machine.getMachineRate().getPriceCustomer(this.getTimestamp());
    }

    public double getPriceCustomerWithDiscount() {
        return (
            this.machine.getMachineRate().getPriceCustomer(this.getTimestamp()) *
            (this.getCard() != null ? 1 - this.getCard().getDiscount() : 1)
        );
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public double getWaterConsumption() {
        return waterConsumption;
    }

    public void setWaterConsumption(double waterConsumption) {
        this.waterConsumption = waterConsumption;
    }

    public double getEnergyConsumption() {
        return energyConsumption;
    }

    public void setEnergyConsumption(double energyConsumption) {
        this.energyConsumption = energyConsumption;
    }

    public boolean isAccredited() {
        return result != null && (ACCREDITED_RESULT.contains(MachineUseResult.getEnum(result)));
    }

    public boolean isMaintenance() {
        return result != null && result.equals(MachineUseResult.IN_MAINTENANCE.getCodeString());
    }

    public void setAccredited(boolean accredited) {
        if (this.card.isPrepaid()) {
            this.setResult(accredited ? "1" : "11");
        } else if (this.card.isPostpaid()) {
            this.setResult(accredited ? "0" : "10");
        }
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    /**
     * @return the alert
     */
    public boolean isAlert() {
        return alert;
    }

    /**
     * @param alert the alert to set
     */
    public void setAlert(boolean alert) {
        this.alert = alert;
    }

    @Override
    public String getItemType() {
        return "MACHINE_USE";
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public String getChannel() {
        return this.channel;
    }

    @Override
    public Unit getUnit() {
        return this.card != null ? this.card.getUnit() : null;
    }

    public List<ExternalSaleNotificationRecord> getExternalSaleNotificationRecords() {
        return this.externalSaleNotificationRecords;
    }

    public ExternalSaleNotificationRecord getLastExternalSaleNotificationRecord() {
        if (this.externalSaleNotificationRecords == null) {
            return null;
        }

        return this.externalSaleNotificationRecords.stream()
            .max(Comparator.comparing(ExternalSaleNotificationRecord::getCreatedAt))
            .orElse(null);
    }

    public Building getBuilding() {
        return this.building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }
}
