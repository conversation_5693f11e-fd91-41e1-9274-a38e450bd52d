package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.*;
import javax.persistence.*;

@Entity
@Table(
    indexes = {
        @Index(name = "PROVIDER_TRANSACTION_ID", columnList = "providerTransactionId"),
        @Index(name = "PUBLIC_ID", columnList = "publicId", unique = true),
    }
)
public class Transaction extends Model<Transaction> {

    public enum ErrorCode {
        OK,
        INCOMPLETE,
        ERROR,
        OTHER,
        ROLLBACK;

        public static ErrorCode valueOf(int value) {
            switch (value) {
                case 0:
                    return ErrorCode.OK;
                case 1:
                    return ErrorCode.INCOMPLETE;
                case 5:
                    return ErrorCode.ERROR;
                case 10:
                    return ErrorCode.ROLLBACK;
                default:
                    return ErrorCode.OTHER;
            }
        }
    }

    public enum AuthorizationResult {
        AUTHORIZED,
        DENIED_BY_BANK,
        DENIED,
        OTHER,
        CANCELLED;

        public static AuthorizationResult valueOf(int value) {
            switch (value) {
                case 0:
                    return AuthorizationResult.AUTHORIZED;
                case 1:
                    return AuthorizationResult.DENIED_BY_BANK;
                case 5:
                    return AuthorizationResult.DENIED;
                case 10:
                    return AuthorizationResult.CANCELLED;
                default:
                    return AuthorizationResult.OTHER;
            }
        }
    }

    public enum ReasonType {
        CREDIT,
        BRANDING,
        OTHER,
    }

    private static final long serialVersionUID = -3557047328270703103L;

    @Id
    @GeneratedValue
    private int id;

    @Column(nullable = false)
    private final String publicId = UUID.randomUUID().toString();

    @OneToMany(mappedBy = "transaction")
    private List<Bill> bills;

    // first step
    @Enumerated(EnumType.STRING)
    private Currency currency;

    private Date creationDate;
    private Date confirmationDate;
    /**
     * Card.uuid
     */
    private String uid;
    private String name;
    private String email;
    private double amount;
    private String serialNumber;
    private String rut;
    private ReasonType reasonType = ReasonType.CREDIT;
    private Integer quantity;
    private String address;

    @OneToOne(cascade = CascadeType.ALL)
    private Unit unit;

    @OneToOne(cascade = CascadeType.ALL)
    private Machine machine;

    @OneToOne
    private BrandingItem brandingItem;

    // second step
    private String authorizationCode;
    private String authorizationResultMessage;
    private String errorMessage;
    private String paymentGatewayId;
    private ErrorCode errorCode;
    private AuthorizationResult authorizationResult;

    private Boolean sendBill;
    private String origin;
    private double comission;

    /**
     * Saves order id reference of each payment gateway
     *   MercadoPago: Preference.id
     *   BambooPayment - PhysicalAgents: order.id
     *   Bancard: process.shop_id
     */
    private String providerTransactionId;

    public Transaction() {
        this.bills = new ArrayList<>();
    }

    public Transaction(
        String name,
        String email,
        Currency currency,
        double amount,
        String uid,
        Unit unit,
        String origin
    ) {
        this();
        this.creationDate = new Date();
        this.name = name;
        this.email = email;
        this.currency = currency;
        this.amount = amount;
        this.uid = uid;
        this.unit = unit;
        this.origin = origin;
        this.rut = null;
    }

    public Transaction(
        String name,
        String email,
        Currency currency,
        double amount,
        int quantity,
        BrandingItem brandingItem,
        String address,
        String origin,
        ReasonType reason
    ) {
        this(name, email, currency, amount, null, null, origin);
        this.quantity = quantity;
        this.reasonType = reason;
        this.brandingItem = brandingItem;
        this.address = address;
    }

    public Transaction(
        String name,
        Currency currency,
        double amount,
        String uid,
        Unit unit,
        String origin,
        ReasonType reason
    ) {
        this(name, currency, amount, uid, unit, origin);
        this.reasonType = reason;
    }

    public Transaction(
        String name,
        Currency currency,
        double amount,
        String uid,
        Unit unit,
        String origin
    ) {
        this();
        this.name = name;
        this.creationDate = new Date();
        this.currency = currency;
        this.amount = amount;
        this.uid = uid;
        this.unit = unit;
        this.origin = origin;
        this.rut = null;
    }

    public Transaction(
        Currency currency,
        double amount,
        String serialNumber,
        Machine machine,
        String origin
    ) {
        this();
        this.creationDate = new Date();
        this.currency = currency;
        this.amount = amount;
        this.origin = origin;
        this.serialNumber = serialNumber;
        this.machine = machine;
        this.rut = null;
    }

    private static Finder<Integer, Transaction> find = new Finder<Integer, Transaction>(
        Integer.class,
        Transaction.class
    );

    public static Transaction findById(int transactionId) {
        return find.byId(transactionId);
    }

    public static List<Transaction> findByUUID(String uuid) {
        return find.query().eq("uid", uuid).findList();
    }

    public static Transaction findLastByUUID(String uuid) {
        return find.query().eq("uid", uuid).orderByDesc("creationDate").setMaxRows(1).findUnique();
    }

    public int getId() {
        return this.id;
    }

    public String getPublicId() {
        return this.publicId;
    }

    public Bill getBill() {
        if (bills == null || bills.isEmpty()) {
            return null;
        }

        Bill mainBill = bills.stream().filter(Bill::isMainBill).findFirst().orElse(null);

        if (mainBill != null) {
            return mainBill;
        }

        return bills.stream().max(Comparator.comparingDouble(Bill::getTotal)).orElse(null);
    }

    public void setBill(Bill bill) {
        this.bills.add(bill);
    }

    public Currency getCurrency() {
        return this.currency;
    }

    public void setCurrency(Currency currency) {
        this.currency = currency;
    }

    public Date getCreationDate() {
        return this.creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Date getConfirmationdate() {
        return this.confirmationDate;
    }

    public void setConfirmationdate(Date confirmationDate) {
        this.confirmationDate = confirmationDate;
    }

    public String getUid() {
        return this.uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEmail() {
        return this.email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public double getAmount() {
        return this.amount;
    }

    public void setAmount(double amount) {
        this.amount = amount;
    }

    public Unit getUnit() {
        return this.unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public Building getBuilding() {
        return this.machine != null ? this.machine.getBuilding() : null;
    }

    public String getAuthorizationcode() {
        return this.authorizationCode;
    }

    public void setAuthorizationcode(String authorizationCode) {
        this.authorizationCode = authorizationCode;
    }

    public String getSerialNumber() {
        return this.serialNumber;
    }

    public String getAuthorizationresultmessage() {
        return this.authorizationResultMessage;
    }

    public void setAuthorizationresultmessage(String authorizationResultMessage) {
        this.authorizationResultMessage = authorizationResultMessage;
    }

    public String getErrormessage() {
        return this.errorMessage;
    }

    public void setErrormessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getPaymentgatewayid() {
        return this.paymentGatewayId;
    }

    public void setPaymentgatewayid(String paymentGatewayId) {
        this.paymentGatewayId = paymentGatewayId;
    }

    public ErrorCode getErrorcode() {
        return this.errorCode;
    }

    public void setErrorcode(ErrorCode errorCode) {
        this.errorCode = errorCode;
    }

    public AuthorizationResult getAuthorizationresult() {
        return this.authorizationResult;
    }

    public void setAuthorizationresult(AuthorizationResult authorizationResult) {
        this.authorizationResult = authorizationResult;
    }

    public Boolean getSendBill() {
        return this.sendBill;
    }

    public void setSendBill(Boolean sendBill) {
        this.sendBill = sendBill;
    }

    public String getOrigin() {
        return this.origin;
    }

    public void setOrigin(String origin) {
        this.origin = origin;
    }

    public double getComission() {
        return this.comission;
    }

    public void setComission(double c) {
        this.comission = Math.round(c * 100.0) / 100.0;
    }

    public void setRut(String r) {
        this.rut = r;
    }

    public String getRut() {
        return this.rut;
    }

    public ReasonType getReasonType() {
        return this.reasonType;
    }

    public void setReasonType(ReasonType reasonType) {
        this.reasonType = reasonType;
    }

    public BrandingItem getBrandingItem() {
        return this.brandingItem;
    }

    public void setBrandingItem(BrandingItem brandingItem) {
        this.brandingItem = brandingItem;
    }

    public Integer getQuantity() {
        return this.quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public String getAddress() {
        return this.address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProviderTransactionId() {
        return this.providerTransactionId;
    }

    public void setProviderTransactionId(String providerTransactionId) {
        this.providerTransactionId = providerTransactionId;
    }

    public Building getRecipientBuilding() {
        return this.machine != null ? this.machine.getBuilding() : null;
    }

    public void confirm() {
        setAuthorizationcode("0");
        setAuthorizationresultmessage("Transacción realizada con éxito");
        setErrorcode(Transaction.ErrorCode.OK);
        setAuthorizationresult(Transaction.AuthorizationResult.AUTHORIZED);
    }
}
