package models;

import java.nio.file.Files;
import java.nio.file.Paths;
import javax.persistence.*;
import utils.StringHelper;

@Entity
@Table(name = "building_reporting_schedule")
public class BuildingReportingSchedule {

    public enum ReportingSchedule {
        DAILY,
        WEEKLY,
        MONTHLY,
    }

    @Id
    @GeneratedValue
    private int id;

    @ManyToOne(cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private BuildingSetting buildingSetting;

    private ReportingSchedule reportingSchedule;

    private String reportingEmail;

    private String customReportingFilePath;

    private String customReportingBuilder;

    private boolean enabled;

    public BuildingReportingSchedule() {}

    public int getId() {
        return this.id;
    }

    public BuildingSetting getBuildingSetting() {
        return this.buildingSetting;
    }

    public ReportingSchedule getReportingSchedule() {
        return this.reportingSchedule;
    }

    public String getReportingEmail() {
        return this.reportingEmail;
    }

    public String getCustomReportingFilePath() {
        return "app/resources/reporting/" + this.customReportingFilePath;
    }

    public String getCustomReportingBuilder() {
        return this.customReportingBuilder;
    }

    public boolean isEnabled() {
        return this.enabled;
    }

    public void setId(int id) {
        this.id = id;
    }

    public void setBuildingSetting(BuildingSetting buildingSetting) {
        this.buildingSetting = buildingSetting;
    }

    public void setReportingSchedule(ReportingSchedule reportingSchedule) {
        this.reportingSchedule = reportingSchedule;
    }

    public void setCustomReportingFilePath(String customReportingFilePath) {
        this.customReportingFilePath = customReportingFilePath;
    }

    public void setReportingEmail(String reportingEmail) {
        this.reportingEmail = reportingEmail;
    }

    public void setCustomReportingBuilder(String customReportingBuilder) {
        this.customReportingBuilder = customReportingBuilder;
    }

    public boolean hasCustomReporting() {
        return (
            !StringHelper.isBlank(this.customReportingFilePath) &&
            Files.exists(Paths.get(this.getCustomReportingFilePath()))
        );
    }
}
