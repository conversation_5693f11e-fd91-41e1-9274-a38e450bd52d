package models;

import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Collection;
import java.util.Date;
import javax.persistence.*;

@Entity
public class InstallationLog extends Model<InstallationLog> implements Comparable<InstallationLog> {

    private static final long serialVersionUID = 521321649324009350L;

    @Id
    @GeneratedValue
    private int id;

    @OneToOne(cascade = CascadeType.ALL)
    private Machine machine;

    @OneToOne(cascade = CascadeType.ALL)
    private Building old_building;

    @OneToOne(cascade = CascadeType.ALL)
    private Building new_building;

    @OneToOne(cascade = CascadeType.ALL)
    private SoapDispenser old_soap_dispenser;

    @OneToOne(cascade = CascadeType.ALL)
    private SoapDispenser new_soap_dispenser;

    private Date installation_date;

    private String message;

    public static Finder<Integer, InstallationLog> find = new Finder<Integer, InstallationLog>(
        Integer.class,
        InstallationLog.class
    );

    public InstallationLog() {}

    public InstallationLog(
        Machine machine,
        Building new_building,
        Building old_building,
        SoapDispenser new_soap_disp,
        SoapDispenser old_soap_disp,
        String msj
    ) {
        this.machine = machine;
        this.old_building = old_building;
        this.new_building = new_building;
        this.old_soap_dispenser = old_soap_disp;
        this.new_soap_dispenser = new_soap_disp;
        this.message = msj;
        this.installation_date = new Date();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public Machine getMachine() {
        return this.machine;
    }

    public void setMachine(Machine machine) {
        this.machine = machine;
    }

    public Building getNewBuilding() {
        return this.new_building;
    }

    public void setNewBuilding(Building bn) {
        this.new_building = bn;
    }

    public Building getOldBuilding() {
        return this.old_building;
    }

    public void setOldBuilding(Building bo) {
        this.old_building = bo;
    }

    public SoapDispenser getNewSD() {
        return this.new_soap_dispenser;
    }

    public void setNewSoapDispenser(SoapDispenser s) {
        this.new_soap_dispenser = s;
    }

    public SoapDispenser getOldSD() {
        return this.old_soap_dispenser;
    }

    public void SetOldSD(SoapDispenser s) {
        this.old_soap_dispenser = s;
    }

    public String getInstallationMessage() {
        return this.message;
    }

    public void setInstallationMessage(String message) {
        this.message = message;
    }

    public static Collection<InstallationLog> findByMachineId(Machine machine) {
        return find.query().eq("machine", machine).eq("machine_id", machine.getId()).findList();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!InstallationLog.class.isAssignableFrom(obj.getClass())) return false;
        return ((InstallationLog) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(InstallationLog o) {
        if (o.id > this.id) return 1; else if (o.id < this.id) return -1; else return 0;
    }

    public static InstallationLog findById(int id) {
        return find.byId(id);
    }
}
