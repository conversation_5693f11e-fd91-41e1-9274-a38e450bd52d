package models;

import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import org.hibernate.criterion.Restrictions;
import org.joda.time.DateTime;

@Entity
@Table(
    indexes = {
        @Index(name = "CREATED_AT", columnList = "created_at"),
        @Index(name = "CREATED_AT_MAINTENANCE_TYPE", columnList = "created_at, maintenance_type"),
        @Index(name = "BUILDING_ID_MAINTENANCE_TYPE", columnList = "building_id, maintenance_type"),
    }
)
public class PreventiveMaintenanceBuildingEntry extends Model<PreventiveMaintenanceBuildingEntry> {

    @EmbeddedId
    private PreventiveMaintenanceBuildingEntryKey key;

    @Transient
    private Building building;

    @Transient
    private MaintenanceParameter maintenanceParameter;

    @Transient
    private Machine machine;

    @Column(nullable = true)
    private Integer machineId;

    @Column(nullable = true)
    private Integer uses;

    @Column(nullable = true)
    private Date maintenanceDate;

    @Column(nullable = true)
    private String technician;

    public PreventiveMaintenanceBuildingEntry() {}

    public static List<PreventiveMaintenanceBuildingEntry> findCurrent() {
        return PreventiveMaintenanceBuildingEntry.findByDate(new Date());
    }

    public static List<PreventiveMaintenanceBuildingEntry> findByDate(Date date) {
        DefaultQuery<PreventiveMaintenanceBuildingEntry> query = (DefaultQuery<PreventiveMaintenanceBuildingEntry>) finder.query();

        Date from = new DateTime(date).millisOfDay().withMinimumValue().toDate();
        Date to = new DateTime(date).millisOfDay().withMaximumValue().toDate();

        query.getCriteria().add(Restrictions.between("key.createdAt", from, to));
        return query.findList();
    }

    protected static Finder<PreventiveMaintenanceBuildingEntryKey, ? extends PreventiveMaintenanceBuildingEntry> finder = new Finder<>(
        PreventiveMaintenanceBuildingEntryKey.class,
        PreventiveMaintenanceBuildingEntry.class
    );

    public PreventiveMaintenanceBuildingEntryKey getKey() {
        return this.key;
    }

    public Building getBuilding() {
        return this.building;
    }

    public void setBuilding(Building building) {
        this.building = building;
    }

    public Integer getMachineId() {
        return this.machineId;
    }

    public Machine getMachine() {
        return this.machine;
    }

    public void setMachine(Machine machine) {
        this.machine = machine;
    }

    public MaintenanceParameter getMaintenanceParameter() {
        return this.maintenanceParameter;
    }

    public String getMaintenanceType() {
        return this.key.getMaintenanceType();
    }

    public Integer getUses() {
        return this.uses;
    }

    public Date getMaintenanceDate() {
        return this.maintenanceDate;
    }

    public String getTechnician() {
        return this.technician;
    }

    public void setMaintenanceParameter(MaintenanceParameter maintenanceParameter) {
        this.maintenanceParameter = maintenanceParameter;
    }

    public Date getCreatedAt() {
        return this.key.getCreatedAt();
    }
}
