package models;

import java.util.Date;

public interface BillableItem {
    void setBill(Bill b);

    int getId();

    Date getTimestamp();

    String getHeadline();

    boolean isAccredited();

    boolean isMaintenance();

    String getReason();

    boolean isAlert();

    Card getCard();

    Unit getUnit();

    Machine getMachine();

    String getResult();

    ExternalSaleNotificationRecord getLastExternalSaleNotificationRecord();
}
