package models;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.play4jpa.jpa.models.DefaultQuery;
import com.play4jpa.jpa.models.Finder;
import com.play4jpa.jpa.models.Model;
import global.APIException;
import global.APIException.APIErrors;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import javax.persistence.CascadeType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.ManyToMany;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Transient;
import org.hibernate.criterion.Restrictions;
import org.hibernate.sql.JoinType;
import play.i18n.Messages;
import play.mvc.Http.Request;

@Entity
public class AuditWorkflow extends Model<AuditWorkflow> implements Comparable<AuditWorkflow> {

    public enum Status {
        IN_PROGRESS,
        COMPLETED,
        ERROR,
        MQTT_ERROR,
    }

    @Id
    @GeneratedValue
    private int transaction_id;

    private Date creationTimestamp;

    private String details;

    @Enumerated(EnumType.STRING)
    private Status status;

    private int original_transaction_id;

    private int user_id;

    private int group_id;

    /**
     * Transaction.id
     */
    private int payment_tx;

    private boolean bot_machine_activation;

    public static Finder<Integer, AuditWorkflow> find = new Finder<Integer, AuditWorkflow>(
        Integer.class,
        AuditWorkflow.class
    );

    public AuditWorkflow() {}

    public AuditWorkflow(String details, Status code) {
        this.details = details;
        this.creationTimestamp = new Date();
        this.status = code;
    }

    public AuditWorkflow(String details, Status code, int original_transaction_id) {
        this.details = details;
        this.creationTimestamp = new Date();
        this.status = code;
        this.original_transaction_id = original_transaction_id;
    }

    public AuditWorkflow(
        String details,
        Status code,
        int original_transaction_id,
        int user_id,
        int group_id
    ) {
        this.details = details;
        this.creationTimestamp = new Date();
        this.status = code;
        this.original_transaction_id = original_transaction_id;
        this.user_id = user_id;
        this.group_id = group_id;
    }

    public int getId() {
        return transaction_id;
    }

    public void setId(int id) {
        this.transaction_id = id;
    }

    public int getOriginalTransactionId() {
        return original_transaction_id;
    }

    public void setOriginalTransactionId(int id) {
        this.original_transaction_id = id;
    }

    public Date getDate() {
        return creationTimestamp;
    }

    public void setDate(Date d) {
        this.creationTimestamp = d;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Status getStatus() {
        return status;
    }

    public void setStatus(Status code) {
        this.status = code;
    }

    public int getUser() {
        return user_id;
    }

    public void setUserId(int u) {
        this.user_id = u;
    }

    public int getGroup() {
        return group_id;
    }

    public void setGroupId(int u) {
        this.group_id = u;
    }

    public int getPaymentId() {
        return payment_tx;
    }

    public void setPaymentId(int tx) {
        this.payment_tx = tx;
    }

    public boolean getBotMachineActivation() {
        return bot_machine_activation;
    }

    public void setBotMachineActivation(boolean b) {
        this.bot_machine_activation = b;
    }

    public static AuditWorkflow findById(int id) {
        return find.byId(id);
    }

    public static AuditWorkflow findByPaymentTx(int tx) {
        return find.query().eq("payment_tx", tx).findUnique();
    }

    public static List<AuditWorkflow> findByOriginalId(int original_id) {
        return find.query().eq("original_transaction_id", original_id).findList();
    }

    public static List<AuditWorkflow> findAll() {
        return find.all();
    }

    @Override
    public boolean equals(Object obj) {
        if (obj == null) return false;
        if (!AuditWorkflow.class.isAssignableFrom(obj.getClass())) return false;
        return ((AuditWorkflow) obj).getId() == this.getId();
    }

    @Override
    public int compareTo(AuditWorkflow o) {
        if (o.transaction_id > this.transaction_id) return 1; else if (
            o.transaction_id < this.transaction_id
        ) return -1; else return 0;
    }
}
