package services;

import dto.support.SendSupportMessageParameters;
import models.BrandingItemRequest;
import models.Building;
import models.Unit;
import utils.email.EmailService;

public class ContactSupportService extends services.BaseService {

    public static final String SUPPORT_INFO_EMAIL = "<EMAIL>";
    public static final String SUPPORT_TECH_EMAIL = "<EMAIL>";
    public static final String SUPPORT_SALES_EMAILS =
        "<EMAIL>,<EMAIL>,<EMAIL>";
    public static final String POSTMAN_EMAIL = "<EMAIL>";
    public static final String APP_USERS_BACKUP_EMAIL = "<EMAIL>";

    protected static final String WHATSAPP_ORIGIN = "Whatsapp Bot";
    protected static final String MOBILE_APP_ORIGIN = "Mobile App";

    public void requestNewCard(SendSupportMessageParameters params) {
        requestNewCard(
            params.getUser().getFirstName(),
            params.getUser().getLastName(),
            params.getUser().getEmailAddress(),
            params.getUser().getPhone(),
            params.getCard().getUnit(),
            params.getCard().getUnit().getBuilding(),
            params.getCard().getUnit().getTower(),
            null,
            MOBILE_APP_ORIGIN
        );
    }

    protected void requestNewCard(
        String firstName,
        String lastName,
        String email,
        String phone,
        Unit unit,
        Building building,
        String unitUserInput,
        String tower,
        String origin
    ) {
        try {
            String subject = "LAVOMAT - Solicitud de nueva tarjeta por " + origin;
            String body = getBody(
                "Se ha recibido una solicitud de creación de nueva tarjeta.",
                "El usuario a ingresado los siguientes datos.",
                new String[][] {
                    { "Nombre", firstName },
                    { "Apellido", lastName },
                    { "E-mail", email },
                    { "Teléfono", phone },
                    { "Apartamento", unit != null ? unit.getNumber() : unitUserInput },
                    { "Torre", tower },
                    { "Edificio Id", building.getId() + "" },
                    { "Nombre del Edificio", building.getName() },
                },
                "Esta petición fue hecha a través de " + origin + "."
            );

            EmailService.sendAsync(SUPPORT_INFO_EMAIL, subject, body);
        } catch (Throwable e) {
            play.Logger.error("Error while contact support service to request new card");
            e.printStackTrace();
        }
    }

    public static void notifyPostman(BrandingItemRequest brandingItemRequest) throws Exception {
        try {
            String subject = "LAVOMAT - Una nueva entrega de un producto de Branding";
            String itemReference = brandingItemRequest.getBrandingItem().getReference();
            String body = getBody(
                "Se ha recibido la compra de un nuevo producto de branding, que debe ser entregada.",
                "La infomación de la misma es la siguiente.",
                new String[][] {
                    { "Id", brandingItemRequest.getId() + "" },
                    { "Nombre del accesorio", brandingItemRequest.getBrandingItem().getName() },
                    { "Email de contacto", brandingItemRequest.getTransaction().getEmail() },
                    { "Dirección", brandingItemRequest.getTransaction().getAddress() },
                    { "Cantidad", brandingItemRequest.getTransaction().getQuantity() + "" },
                },
                "Para ver mas informacion ir a: https://www.lavomat.com.uy/accessories#" +
                itemReference
            );
            EmailService.sendAsync(POSTMAN_EMAIL, subject, body);
        } catch (Exception e) {
            play.Logger.error("Send email when notify postman log an error. ");
            e.printStackTrace();
            throw e;
        }
    }

    public void requestCardRelease(SendSupportMessageParameters params) {
        try {
            String subject = "LAVOMAT - Solicitud de liberar tarjeta por " + MOBILE_APP_ORIGIN;
            String body = getBody(
                "Se ha recibido una solicitud de liberación de una tarjeta.",
                "El usuario a ingresado los siguientes datos.",
                new String[][] {
                    { "Nombre", params.getUser().getFirstName() },
                    { "Apellido", params.getUser().getLastName() },
                    { "E-mail", params.getUser().getEmailAddress() },
                    { "Teléfono", params.getUser().getPhone() },
                    { "Tarjeta", params.getCard().getUuid() },
                    { "Apartamento", params.getCard().getUnit().getNumber() },
                    { "Edificio Id", params.getCard().getUnit().getBuilding().getId() + "" },
                    { "Nombre del Edificio", params.getCard().getUnit().getBuilding().getName() },
                },
                "Esta petición fue hecha a través de " + MOBILE_APP_ORIGIN + "."
            );

            EmailService.sendAsync(SUPPORT_INFO_EMAIL, subject, body);
        } catch (Throwable e) {
            play.Logger.error("Error while contact support service to request new card");
            e.printStackTrace();
        }
    }

    public void requestCardBlock(SendSupportMessageParameters params) {
        try {
            String subject = "LAVOMAT - Solicitud de bloquear tarjeta por " + MOBILE_APP_ORIGIN;
            String body = getBody(
                "Se ha recibido una solicitud de bloqueo de una tarjeta.",
                "El usuario a ingresado los siguientes datos.",
                new String[][] {
                    { "Nombre", params.getUser().getFirstName() },
                    { "Apellido", params.getUser().getLastName() },
                    { "E-mail", params.getUser().getEmailAddress() },
                    { "Teléfono", params.getUser().getPhone() },
                    { "Tarjeta", params.getCard().getUuid() },
                    { "Apartamento", params.getCard().getUnit().getNumber() },
                    { "Edificio Id", params.getCard().getUnit().getBuilding().getId() + "" },
                    { "Nombre del Edificio", params.getCard().getUnit().getBuilding().getName() },
                },
                "Esta petición fue hecha a través de " + MOBILE_APP_ORIGIN + "."
            );

            EmailService.sendAsync(SUPPORT_INFO_EMAIL, subject, body);
        } catch (Throwable e) {
            play.Logger.error("Error while contact support service to request new card");
            e.printStackTrace();
        }
    }

    protected static String getBody(
        String introduction,
        String optionsTitle,
        String[][] options,
        String note
    ) {
        return views.html.contactSupport.render(introduction, optionsTitle, options, note).body();
    }
}
