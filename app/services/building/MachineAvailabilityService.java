package services.building;

import global.APIException;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import models.*;
import queries.mappings.TotemMachineQuery;
import services.BaseService;

public class MachineAvailabilityService extends BaseService {

    private Building building;
    private User totemUser;
    private final Date olderAliveAllowed;

    private MachineAvailabilityService() {
        this.olderAliveAllowed = getTwentyMinutesAgo();
    }

    public MachineAvailabilityService(Building building) {
        this();
        this.building = building;
    }

    public MachineAvailabilityService(int buildingId, User totemUser) throws APIException {
        this();
        this.building = Building.findById(buildingId);
        if (this.building == null) {
            throw APIException
                .raise(APIException.APIErrors.BUILDING_NOT_FOUND)
                .setDetailMessage("BUILDING_NOT_FOUND");
        }

        if (totemUser != null && Role.TOTEM == totemUser.getRole()) {
            this.totemUser = totemUser;
        }
    }

    public List<Machine> getMachines() {
        List<Machine> machines = getAllowedMachines();

        List<Machine> availableParentMachines = machines
            .stream()
            .filter(this::isMachineLastAliveIsNotTooOld)
            .collect(Collectors.toList());

        List<Machine> availableChildMachines = availableParentMachines
            .stream()
            .map(Machine::getRPIChild)
            .filter(Objects::nonNull)
            .filter(machine -> machine.getLastAlive() == null)
            .collect(Collectors.toList());

        List<Machine> allAvailableMachines = Stream
            .concat(availableParentMachines.stream(), availableChildMachines.stream())
            .distinct()
            .filter(m -> m.getSortIndex() != Machine.DISABLED_SORT_INDEX)
            .collect(Collectors.toList());

        logger("Machines available: {}", allAvailableMachines.size());

        allAvailableMachines.sort(Comparator.comparingInt(Machine::getSortIndex));

        return allAvailableMachines;
    }

    public Date getLastWashMaintenance() {
        Maintenance washerMaintenance = Maintenance.getLastMaintenanceByBuildingId(
            this.building.getId(),
            Machine.MachineType.WASHER
        );

        return washerMaintenance != null ? washerMaintenance.getTimestamp() : null;
    }

    public Date getLastDryMaintenance() {
        Maintenance dryerMaintenance = Maintenance.getLastMaintenanceByBuildingId(
            this.building.getId(),
            Machine.MachineType.DRYER
        );

        return dryerMaintenance != null ? dryerMaintenance.getTimestamp() : null;
    }

    private Date getTwentyMinutesAgo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -20);

        return calendar.getTime();
    }

    private List<Machine> getAllowedMachines() {
        List<Machine> machines = this.building.getMachines();

        if (this.totemUser == null) return machines;

        List<TotemUserMachineMap> mappings = new TotemMachineQuery()
            .filterByTotemUserId(this.totemUser.getId())
            .find();

        if (mappings.isEmpty()) return machines;

        List<Integer> machineIds = mappings
            .stream()
            .map(mapping -> mapping.getMachine().getId())
            .collect(Collectors.toList());

        return machines
            .stream()
            .filter(machine -> machineIds.contains(machine.getId()))
            .collect(Collectors.toList());
    }

    private boolean isMachineLastAliveIsNotTooOld(Machine machine) {
        return (
            machine.getLastAlive() != null && machine.getLastAlive().after(this.olderAliveAllowed)
        );
    }
}
