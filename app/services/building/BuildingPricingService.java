package services.building;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import models.Building;
import models.Machine;
import queries.soap_dispensers.SoapDispenserQuery;
import services.BaseService;

public class BuildingPricingService extends BaseService {

    private final Building building;

    public BuildingPricingService(Building building) {
        this.building = building;
    }

    public List<PricingRule> getPricingRules() {
        return this.getMachines()
            .stream()
            .map(machine ->
                new PricingRule(
                    machine.getMachineRate().getName(),
                    machine.getCapacity(),
                    new SoapDispenserQuery().filterByMachineId(machine.getId()).any(),
                    machine.getMachineRate().getPriceCustomer()
                )
            )
            .distinct()
            .collect(Collectors.toList());
    }

    private List<Machine> getMachines() {
        MachineAvailabilityService service = new MachineAvailabilityService(this.building);

        return service.getMachines();
    }

    public static class PricingRule {

        private final String name;
        private final int capacity;
        private final boolean hasSoapDispenser;
        private final Double priceCustomer;

        public PricingRule(
            String name,
            int capacity,
            boolean hasSoapDispenser,
            Double priceCustomer
        ) {
            this.name = name;
            this.capacity = capacity;
            this.hasSoapDispenser = hasSoapDispenser;
            this.priceCustomer = priceCustomer;
        }

        public String getName() {
            return this.name;
        }

        public int getCapacity() {
            return this.capacity;
        }

        public boolean hasSoapDispenser() {
            return this.hasSoapDispenser;
        }

        public Double getPriceCustomer() {
            return this.priceCustomer;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;

            PricingRule other = (PricingRule) obj;
            return (
                this.capacity == other.capacity &&
                this.hasSoapDispenser == other.hasSoapDispenser &&
                Objects.equals(this.name, other.name) &&
                Objects.equals(this.priceCustomer, other.priceCustomer)
            );
        }

        @Override
        public int hashCode() {
            return Objects.hash(
                this.name,
                this.capacity,
                this.hasSoapDispenser,
                this.priceCustomer
            );
        }
    }
}
