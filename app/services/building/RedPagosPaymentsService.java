package services.building;

import domains.billing.BillingService;
import global.APIException;
import java.io.File;
import java.io.FileInputStream;
import java.util.*;
import java.util.stream.Collectors;
import models.*;
import models.Currency;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.CellType;
import org.joda.time.DateTime;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import utils.RedPagosPayment;

public class RedPagosPaymentsService extends BillingService {

    private int failed;
    private int success;
    private List<Integer> failedIndexes;

    public RedPagosPaymentsService() {
        failed = 0;
        success = 0;
        failedIndexes = null;
    }

    /**
     * Given a file provided from RedPagos, which lists the accreditations done by the user at their locals,
     * it is increased cards balances and created bills for.
     * @param file formated file. Requires closureData in columns in the following order:
     * year - month - unit - building - amount - coin - subagency - date - cashier - transaction
     */
    public JSONObject process(File file) throws APIException {
        failed = 0;
        success = 0;
        failedIndexes = new ArrayList();

        List<RedPagosPayment> payments = getData(file);

        List<RedPagosPayment> unprocessedPayments = updateBalanceOfCardsByPayments(payments);

        return generateResponse(unprocessedPayments);
    }

    private List<RedPagosPayment> getData(File file) throws APIException {
        List<RedPagosPayment> payments = new ArrayList();

        try {
            HSSFWorkbook workbook = new HSSFWorkbook(new FileInputStream(file));
            HSSFSheet worksheet = workbook.getSheetAt(0);

            for (int rowIndex = 1; rowIndex <= worksheet.getLastRowNum(); rowIndex++) {
                try {
                    HSSFRow row = worksheet.getRow(rowIndex);

                    RedPagosPayment payment = getPaymetByExcelRow(row);
                    payments.add(payment);

                    success++;
                } catch (Exception e) {
                    failed++;
                    failedIndexes.add(rowIndex + 1);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw APIException.raise(APIException.APIErrors.WRONG_FORMAT);
        }

        return payments;
    }

    private RedPagosPayment getPaymetByExcelRow(HSSFRow row) {
        HSSFCell yearCell = row.getCell(0);
        yearCell.setCellType(CellType.STRING);
        String year = yearCell.getStringCellValue();

        HSSFCell monthCell = row.getCell(1);
        monthCell.setCellType(CellType.STRING);
        String month = monthCell.getStringCellValue();

        HSSFCell unitCell = row.getCell(2);
        unitCell.setCellType(CellType.STRING);
        String unit = unitCell.getStringCellValue();

        String building = row.getCell(3).getStringCellValue();

        Double amount = row.getCell(4).getNumericCellValue();

        String coin = row.getCell(5).getStringCellValue();

        HSSFCell subagencyCell = row.getCell(6);
        subagencyCell.setCellType(CellType.STRING);
        String subagency = subagencyCell.getStringCellValue();

        HSSFCell dateCell = row.getCell(7);
        dateCell.setCellType(CellType.STRING);
        String date = dateCell.getStringCellValue();

        HSSFCell cashierCell = row.getCell(8);
        cashierCell.setCellType(CellType.STRING);
        String cashier = cashierCell.getStringCellValue();

        HSSFCell transactionCell = row.getCell(9);
        transactionCell.setCellType(CellType.STRING);
        String transaction = transactionCell.getStringCellValue();

        return new RedPagosPayment(
            year,
            month,
            unit,
            building,
            amount,
            coin,
            subagency,
            date,
            cashier,
            transaction
        );
    }

    private List<RedPagosPayment> updateBalanceOfCardsByPayments(List<RedPagosPayment> payments) {
        List<RedPagosPayment> errors = new ArrayList();

        // group by building name
        Map<String, List<RedPagosPayment>> paymentsByBuilding = payments
            .stream()
            .collect(Collectors.groupingBy(x -> x.getBuilding()));

        for (String buildingName : paymentsByBuilding.keySet()) {
            List<RedPagosPayment> paymentsOfBuilding = paymentsByBuilding.get(buildingName);
            Building building = Building.findByName(buildingName);

            for (RedPagosPayment payment : paymentsOfBuilding) {
                try {
                    if (building == null) {
                        throw APIException.raise(APIException.APIErrors.BUILDING_NOT_FOUND);
                    }
                    Optional<Unit> oUnit = building
                        .getUnits()
                        .stream()
                        .filter(x -> x.getNumber().equals(payment.getUnit()))
                        .findFirst();
                    if (!oUnit.isPresent()) {
                        throw APIException.raise(APIException.APIErrors.UNIT_NOT_FOUND);
                    }
                    Unit unit = oUnit.get();

                    // search if there are some pending cards replacment
                    // TODO: check this filter
                    Date from = new DateTime().minusMonths(6).toDate();
                    Date to = new Date();
                    List<CardEvent> additionalCards = unit.getCardReplacements(from, to);

                    // discount cards
                    Double additionalCardsDiscount = additionalCards
                        .stream()
                        .mapToDouble(x -> x.getPrecioUnitarioReal())
                        .sum();
                    Double finalAmount = payment.getAmount() - additionalCardsDiscount;

                    // update balance of the first active card from the unit (it should be the user
                    // predefined one)
                    Optional<Card> oCard = Card
                        .findActivesByUnitId(unit.getId())
                        .stream()
                        .filter(x ->
                            x.getContractType() == Card.ContractType.PREPAID &&
                            x.getState() == models.Part.PartState.ACTIVE
                        )
                        .findFirst();

                    if (!oCard.isPresent()) {
                        throw APIException.raise(APIException.APIErrors.CARD_NOT_FOUND);
                    }
                    Card card = oCard.get();
                    Audit a = new Audit(
                        Audit.TransferType.CREDIT,
                        finalAmount,
                        Currency.UYU,
                        card.getUuid().toString(),
                        card.getUuid().toString(),
                        "REDPAGOS",
                        0,
                        Audit.ResultCode.OK,
                        card.getBalance(),
                        card.getBalance() + finalAmount
                    );
                    a.save();
                    card.setBalance(card.getBalance() + finalAmount);
                    card.update();

                    createBillToRedPagosPayment(unit, finalAmount, additionalCards, card.getUuid());
                } catch (Exception e) {
                    e.printStackTrace();
                    errors.add(payment);
                }
            }
        }

        return errors;
    }

    private void createBillToRedPagosPayment(
        Unit unit,
        Double amount,
        List<CardEvent> additionalCards,
        String uid
    ) {
        List<BillItem> items = new ArrayList<>();

        boolean appliesIVA = true; // by default applies IVA

        BillItem item = new BillItem();
        item.setItemType("ACCCREDIT_BALANCE");
        item.setItemMeasureUnit(BillItem.MEASURE_UNIT_BALANCE);
        item.setItemName("Acreditar Saldo");
        item.setItemUnitPrice(amount / 1.22);
        item.setItemIndDet(appliesIVA ? 3 : 1);
        item.setAmount(1);

        items.add(item);
        items.addAll(toBillItems(additionalCards));

        Bill bill = new Bill(unit, items, null, null);
        bill.save();

        publishBill(bill);

        confirmBilling(additionalCards, bill);

        saveTransactionEvent(bill, amount, unit, uid);
    }

    private void saveTransactionEvent(Bill bill, Double amount, Unit unit, String uid) {
        try {
            Transaction transaction = new Transaction(
                "REDPAGOS_User",
                Currency.UYU,
                amount.intValue(),
                uid,
                unit,
                "REDPAGOS_Excel"
            );
            transaction.setBill(bill);

            transaction.save();
        } catch (Exception e) {
            loggerError("Error creating the Transaction to RedPagosPaymentsService.process");

            e.printStackTrace();
        }
    }

    private JSONObject generateResponse(List<RedPagosPayment> unprocessedPayments) {
        JSONObject response = new JSONObject();

        try {
            response.put("processed_ok", success);
            response.put("processed_error", failed);

            JSONArray failedIndexesJson = new JSONArray();
            for (Integer i : failedIndexes) {
                failedIndexesJson.put(i);
            }
            response.put("processed_error_index", failedIndexesJson);

            JSONArray unprocessedPaymentsJson = new JSONArray();
            for (RedPagosPayment p : unprocessedPayments) {
                unprocessedPaymentsJson.put(p.getFullLine());
            }
            response.put("processed_error_payments", unprocessedPaymentsJson);
        } catch (JSONException e) {
            loggerError("Error creating the json response to RedPagosPaymentsService.process");
            e.printStackTrace();
        }

        return response;
    }
}
