package services.building;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import models.*;
import org.joda.time.DateTime;
import services.BaseService;
import services.user.UsesAccreditationService;
import utils.DateHelper;

public class ColivingBuildingService extends BaseService {

    // Back to 3 backward periods when db is consistent
    public static final int backwardBillingPeriod = 1;

    public void rechargeColivingBuildingsUses() {
        List<Building> buildings = Building.findByBuildingType(BuildingType.COLIVING);

        if (buildings == null) {
            return;
        }

        UsesAccreditationService usesAccreditationService = new UsesAccreditationService();
        for (int i = 0; i < buildings.size(); i++) {
            Building building = buildings.get(i);
            usesAccreditationService.rechargePrepaidUsesToColiving(building, DateTime.now());
        }
    }

    public List<MachineUse> getColivingUsesForClosureByBuilding(
        Building building,
        final Date from,
        final Date to
    ) {
        List<MachineUse> usesToBill = new ArrayList<>();

        List<Unit> units = building.getUnits();
        for (int j = 0; j <= backwardBillingPeriod; j++) {
            DateTime fromDateTime = new DateTime(from).minusMonths(j);
            DateTime toDateTime = new DateTime(to).minusMonths(j);

            for (int i = 0; i < units.size(); i++) {
                usesToBill.addAll(
                    getColivingUsesForClosureByUnit(
                        units.get(i),
                        fromDateTime.toDate(),
                        toDateTime.toDate()
                    )
                );
            }
        }
        return usesToBill;
    }

    public List<MachineUse> getColivingUsesForClosureByUnit(
        Unit unit,
        final Date from,
        final Date to
    ) {
        Date fromFilter = from;
        Date toFilter = to;
        List<MachineUse> usesToBill = new ArrayList<>();
        Building building = unit.getBuilding();

        Account account = validateOwnerAccount(unit);

        if (building.isColiving() && account != null) {
            DateTime[] validatedUserPeriod = validateUserPeriod(account, to);
            fromFilter = validatedUserPeriod[0].toDate();
            toFilter = validatedUserPeriod[1].toDate();
        }

        List<Card> assignedCards = validateAssignedCards(unit);
        if (assignedCards == null || assignedCards.isEmpty()) {
            return usesToBill;
        }

        for (int j = 0; j < assignedCards.size(); j++) {
            Card card = assignedCards.get(j);
            if (!card.isPrepaid()) {
                continue;
            }

            List<MachineUse> usesFound = MachineUse.findForBillingByCard(
                fromFilter,
                toFilter,
                card.getId(),
                building.getPrepaidRechargeableUses(),
                MachineUseResult.PREPAID_ACTIVATION_WITH_BALANCE,
                MachineUseResult.WHATSAPP_ACTIVATION_WITH_BALANCE
            );

            logger(
                "Account day: {} - card: {} - from: {} - to: {}",
                account != null ? account.getCreationTimestamp() : "no acc",
                card.getUuid(),
                fromFilter,
                toFilter
            );

            List<MachineUse> unbilledUses = usesFound
                .stream()
                .filter(use -> use.getBill() == null || use.getBill().isCreditNote())
                .collect(Collectors.toList());

            int billedUsesCount = usesFound.size() - unbilledUses.size();
            int usesToBillInThePeriodCount = 0;
            for (MachineUse use : unbilledUses) {
                if (
                    (billedUsesCount + usesToBillInThePeriodCount) <
                    building.getPrepaidRechargeableUses()
                ) {
                    usesToBill.add(use);
                    usesToBillInThePeriodCount++;
                } else {
                    break;
                }
            }
        }

        return usesToBill;
    }

    public Account validateOwnerAccount(Unit unit) {
        User owner = unit.getOwner();
        if (owner == null) {
            return null;
        }

        Account account = owner.getMasterAccount();

        if (
            !owner.isActiveUser() ||
            !account.getValidated() ||
            account.getCreationTimestamp() == null
        ) {
            logger(
                "Account {} not validated, user not active or missing CreationTimestamp",
                account.getId()
            );
            return null;
        }

        return account;
    }

    public List<Card> validateAssignedCards(Unit unit) {
        List<Card> assignedCards = unit.getAssignedCards();
        if (assignedCards == null || assignedCards.isEmpty()) {
            logger("Unit {} has no cards assigned", unit.getNumber());
        }

        return assignedCards;
    }

    public Date[] validateAccountCreationDay(
        Account account,
        Building building,
        final Date from,
        final Date to
    ) {
        Date usesFromFilter = from;
        Date usesToFilter = to;
        if (
            account != null &&
            building.isColiving() &&
            DateHelper.isMergedDay(new DateTime(account.getCreationTimestamp()))
        ) {
            // TODO: link doc where this is specified
            usesFromFilter = new DateTime(from).toDate();
            usesToFilter = new DateTime(to).withDayOfMonth(27).toDate();
        }
        return new Date[] { usesFromFilter, usesToFilter };
    }

    public DateTime[] validateUserPeriod(Account account, final Date to) {
        DateTime accountCreationDateTime = new DateTime(account.getCreationTimestamp());
        int accountCreationDayOfMonth = accountCreationDateTime.getDayOfMonth();

        DateTime fromFilter;
        DateTime toFilter;
        DateTime base = new DateTime(to);
        // Calculate the end date
        if (accountCreationDayOfMonth <= 28) {
            toFilter =
                base
                    .withDayOfMonth(accountCreationDayOfMonth)
                    .minusDays(1)
                    .millisOfDay()
                    .withMaximumValue();
        } else {
            toFilter = base.withDayOfMonth(28).millisOfDay().withMaximumValue();
        }

        // Calculate the start date
        if (accountCreationDayOfMonth <= 28) {
            fromFilter =
                base
                    .minusMonths(1)
                    .withDayOfMonth(accountCreationDayOfMonth)
                    .withTimeAtStartOfDay();
        } else {
            fromFilter = base.minusMonths(1).withDayOfMonth(28).plusDays(1).withTimeAtStartOfDay();
        }

        return new DateTime[] { fromFilter, toFilter };
    }
}
