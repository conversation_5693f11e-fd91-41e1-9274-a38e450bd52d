package services;

import dto.maintenance.CreateMaintenanceParameters;
import dto.maintenance.UpdateMaintenanceParameters;
import global.APIException;
import global.exceptions.machines.MachineAlreadyInMaintenanceException;
import global.exceptions.machines.MachineNotAvailableException;
import global.exceptions.machines.MachineNotFoundException;
import global.exceptions.machines.MachineNotInMaintenanceException;
import java.util.Date;
import java.util.List;
import models.*;
import models.Maintenance.MaintenanceType;
import queries.buildings.BuildingQuery;
import queries.machines.MachineQuery;
import services.machine.MachineUsabilityService;
import utils.DateHelper;

public class MaintenanceService {

    protected static final String DRYER_ASSIGNATION = "Primera Asignación Secadora";
    protected static final String WASHER_ASSIGNATION = "Primera Asignación Lavadora";

    public void createMaintenance(CreateMaintenanceParameters dto) {
        if (dto.isMP1200()) {
            Maintenance maintenance = new Maintenance(
                dto.getBuilding(),
                MaintenanceType.MP1200,
                dto.getTechnician(),
                dto.getTimestamp()
            );
            maintenance.save();
        } else {
            for (Integer type : dto.getMaintenanceTypeCodes()) {
                if (
                    type.equals(Maintenance.MaintenanceType.MP500.getCode()) ||
                    type.equals(Maintenance.MaintenanceType.MP100.getCode())
                ) {
                    Maintenance maintenance = new Maintenance(
                        dto.getBuilding(),
                        MaintenanceType.find(type),
                        dto.getTechnician(),
                        dto.getTimestamp()
                    );
                    maintenance.save();
                }
            }
        }
    }

    public void updateMaintenance(UpdateMaintenanceParameters dto) {
        Maintenance maintenance = dto.getMaintenance();
        MaintenanceType maintenanceType = dto.getMaintenanceType();
        String technician = dto.getTechnician();
        Date timestamp = dto.getTimestamp();

        boolean changed = false;
        if (maintenance.getMaintenanceType() != maintenanceType) {
            maintenance.setMaintenanceType(maintenanceType);
            changed = true;
        }

        if (!maintenance.getTechnician().equals(technician)) {
            maintenance.setTechnician(technician);
            changed = true;
        }

        if (maintenance.getTimestamp() != timestamp) {
            maintenance.setTimestamp(timestamp);
            changed = true;
        }

        if (changed) {
            maintenance.update();
        }
    }

    public void assignMaintenanceParameterToBuilding(
        Building building,
        MaintenanceParameter parameter
    ) {
        building.setMaintenanceParameter(parameter);
        building.update();
    }

    public void assignMaintenanceParameterToMachineModel(
        MachineModel model,
        MaintenanceParameter parameter
    ) {
        model.setParameter(parameter);
        model.update();
    }

    public void assignMaintenanceToNewMachine(Machine machine) {
        if (
            machine.getMachineType() == Machine.MachineType.DRYER &&
            machine.getBuilding().getDryersCount() == 0
        ) {
            Maintenance mp1200 = new Maintenance(
                machine.getBuilding(),
                MaintenanceType.MP1200,
                DRYER_ASSIGNATION,
                new Date()
            );
            mp1200.save();
        } else if (
            machine.getMachineType() == Machine.MachineType.WASHER &&
            machine.getBuilding().getWashersCount() == 0
        ) {
            Maintenance mp500 = new Maintenance(
                machine.getBuilding(),
                MaintenanceType.MP500,
                WASHER_ASSIGNATION,
                new Date()
            );
            mp500.save();

            Maintenance mp100 = new Maintenance(
                machine.getBuilding(),
                MaintenanceType.MP100,
                WASHER_ASSIGNATION,
                new Date()
            );
            mp100.save();
        }
    }

    public List<PreventiveMaintenanceBuildingEntry> mapBuildingEntriesWithMaintenanceParameters()
        throws APIException {
        try {
            List<PreventiveMaintenanceBuildingEntry> buildingMaintenanceEntries =
                this.getPreventiveMaintenanceBuildingEntries();

            for (PreventiveMaintenanceBuildingEntry buildingMaintenanceEntry : buildingMaintenanceEntries) {
                Building building = new BuildingQuery()
                    .get(buildingMaintenanceEntry.getKey().getBuildingId());
                buildingMaintenanceEntry.setBuilding(building);

                MachineModel model = null;
                Integer machineId = buildingMaintenanceEntry.getMachineId();
                if (machineId != null) {
                    Machine machine = Machine.findById(machineId);
                    if (machine == null) {
                        continue;
                    }
                    model = machine.getMachineModel();
                    buildingMaintenanceEntry.setMachine(machine);
                }

                MaintenanceParameter parameter = MaintenanceParameter.getMaintenanceParameterForBuildingAndMachineModel(
                    building,
                    model
                );
                buildingMaintenanceEntry.setMaintenanceParameter(parameter);
            }

            return buildingMaintenanceEntries;
        } catch (Exception e) {
            throw APIException.raise(APIException.APIErrors.PREVENTIVE_MAINTENANCE_REPORT_ERROR, e);
        }
    }

    //    public List<PreventiveMaintenanceMachineEntry> mapMachineEntriesWithMaintenanceParameters(
    //        final int buildingId
    //    ) {
    //        if (this.machineMaintenanceEntries != null && !this.machineMaintenanceEntries.isEmpty()) {
    //            return this.machineMaintenanceEntries;
    //        }
    //        Building building = Building.findById(buildingId);
    //        List<Machine> buildingMachines = building.getMachines();
    //        this.machineMaintenanceEntries = new ArrayList<PreventiveMaintenanceMachineEntry>();

    ////        this.getPreventiveMaintenanceMachineEntries(buildingId);
    //
    //        String[] mps = MaintenanceType.getNames();
    //        for (int i = 0; i < buildingMachines.size(); i++) {
    //            for (int j = 0; j < mps.length; j++) {
    //                String mp = mps[j];
    //                Machine buildingMachine = buildingMachines.get(i);
    //                Optional<PreventiveMaintenanceMachineEntry> existingEntry =
    //                    this.machineMaintenanceEntries.stream()
    //                        .filter(e ->
    //                            e.getMachine().getId() == buildingMachine.getId() &&
    //                            e.getMachineType().equals(mp)
    //                        )
    //                        .findAny();
    //
    //                if (existingEntry == null) {
    //                    PreventiveMaintenanceMachineEntry toAdd = new PreventiveMaintenanceMachineEntry(
    //                        building,
    //                        buildingMachine,
    //                        mp
    //                    );
    //                    this.machineMaintenanceEntries.add(toAdd);
    //                }
    //            }
    //        }
    //
    //        return this.machineMaintenanceEntries;
    //    }
    public List<PreventiveMaintenanceBuildingEntry> getPreventiveMaintenanceBuildingEntries() {
        return PreventiveMaintenanceBuildingEntry.findCurrent();
    }

    //    public void getPreventiveMaintenanceMachineEntries(final int buildingId) {
    //        Building building = Building.findById(buildingId);
    //
    //        try {
    //            String sql =
    //                "SELECT mu.machine_id, ax.maintenance_type, ax.technician, ax.auxDate, COUNT(mu.machine_id) AS uses " +
    //                "FROM part p " +
    //                "LEFT JOIN machine_use mu ON p.id = mu.machine_id " +
    //                "LEFT JOIN (SELECT mm.building_id, mm.maintenance_type, mm.technician, max.auxDate " +
    //                "FROM (SELECT building_id, maintenance_type, MAX(timestamp) AS auxDate " +
    //                "FROM maintenance " +
    //                "GROUP BY building_id, maintenance_type) max " +
    //                "INNER JOIN maintenance mm ON max.building_id = mm.building_id " +
    //                "WHERE max.maintenance_type = mm.maintenance_type " +
    //                "AND max.auxDate = mm.timestamp) ax " +
    //                "ON ax.building_id = p.building_id " +
    //                "AND p.machine_type = CASE WHEN ax.maintenance_type IN ('MP100', 'MP500') THEN 'WASHER' " +
    //                "ELSE 'DRYER' END " +
    //                "LEFT JOIN machine_model model ON p.machinemodel_id = model.id " +
    //                "WHERE p.from_class = 'Machine' " +
    //                "AND p.building_id = " +
    //                buildingId +
    //                " " +
    //                "AND mu.timestamp > IFNULL(ax.auxDate, \"1900-01-01\") " +
    //                "AND mu.result IN ('0', '1', '5', '6', '7') " +
    //                "GROUP BY mu.machine_id, ax.maintenance_type, ax.technician, ax.auxDate " +
    //                "ORDER BY mu.machine_id, ax.maintenance_type;";
    //
    //            EntityManager em = JPA.em();
    //            List<Object[]> query = em.createNativeQuery(sql).getResultList();
    //            for (Object[] a : query) {
    //                PreventiveMaintenanceMachineEntry entry = new PreventiveMaintenanceMachineEntry(
    //                    a[0],
    //                    a[1],
    //                    a[2],
    //                    a[3],
    //                    a[4],
    //                    building
    //                );
    //                this.machineMaintenanceEntries.add(entry);
    //            }
    //        } catch (Exception e) {
    //            e.printStackTrace();
    //        }
    //    }

    //    public List<MachineAverageUses> listMachinesAverageUses(final int buildingId) {
    //        List<MachineAverageUses> machineAverageUses = new ArrayList<>();
    //
    //        try {
    //            Calendar c = Calendar.getInstance();
    //            c.add(Calendar.MONTH, -1);
    //            SimpleDateFormat dateOnly = new SimpleDateFormat("yyyy/MM/dd");
    //            String thirtyDaysAgo = dateOnly.format(c.getTime());
    //            c.add(Calendar.MONTH, -5);
    //            String sixMonthsAgo = dateOnly.format(c.getTime());
    //            c.add(Calendar.MONTH, -6);
    //            String oneYearAgo = dateOnly.format(c.getTime());
    //
    //            String sql =
    //                "SELECT p.id, IFNULL(thirtyDays.uses, 0)/(IFNULL(thirtyDays.thirtyWorkingDays, 1)) AS thirtyDays, " +
    //                "IFNULL(sixMonths.uses, 0)/(IFNULL(sixMonths.sixMonthsWorkingDays, 1)) AS sixMonths, " +
    //                "IFNULL(oneYear.uses, 0)/(IFNULL(oneYear.oneYearWorkingDays, 1)) AS oneYear, " +
    //                "IFNULL(total.totalUses, 0) AS totalUses " +
    //                "FROM part p " +
    //                "LEFT JOIN (SELECT mu.machine_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS thirtyWorkingDays, COUNT(*) AS uses " +
    //                "FROM machine_use mu " +
    //                "WHERE mu.timestamp > '" +
    //                thirtyDaysAgo +
    //                "' " +
    //                "AND mu.result IN ('0', '1', '5', '6', '7') " +
    //                "GROUP BY mu.machine_id) thirtyDays " +
    //                "ON p.id = thirtyDays.machine_id " +
    //                "LEFT JOIN (SELECT mu.machine_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS sixMonthsWorkingDays, COUNT(*) AS uses " +
    //                "FROM machine_use mu " +
    //                "WHERE mu.timestamp > '" +
    //                sixMonthsAgo +
    //                "' " +
    //                "AND mu.result IN ('0', '1', '5', '6', '7') " +
    //                "GROUP BY mu.machine_id) sixMonths " +
    //                "ON p.id = sixMonths.machine_id " +
    //                "LEFT JOIN (SELECT mu.machine_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS oneYearWorkingDays, COUNT(*) AS uses " +
    //                "FROM machine_use mu " +
    //                "WHERE mu.timestamp > '" +
    //                oneYearAgo +
    //                "' " +
    //                "AND mu.result IN ('0', '1', '5', '6', '7') " +
    //                "GROUP BY mu.machine_id) oneYear " +
    //                "ON p.id = oneYear.machine_id " +
    //                "LEFT JOIN (SELECT mu.machine_id, count(*) AS totalUses " +
    //                "FROM machine_use mu " +
    //                "WHERE mu.result IN ('0', '1', '5', '6', '7') " +
    //                "GROUP BY mu.machine_id) total " +
    //                "ON p.id = total.machine_id " +
    //                "WHERE p.building_id = " +
    //                buildingId +
    //                ";";
    //
    //            EntityManager em = JPA.em();
    //            List<Object[]> query = em.createNativeQuery(sql).getResultList();
    //            for (Object[] a : query) {
    //                MachineAverageUses entry = new MachineAverageUses(a[0], a[1], a[2], a[3], a[4]);
    //                machineAverageUses.add(entry);
    //            }
    //        } catch (Exception e) {
    //            e.printStackTrace();
    //        }
    //        return machineAverageUses;
    //    }

    public List<BuildingAverageUses> listBuildingsAverageUses() {
        return BuildingAverageUses.findCurrent();
    }

    public void setMachineIntoMaintenance(int machineId) throws APIException {
        Machine machine = new MachineQuery().get(machineId);
        if (machine == null) {
            throw new MachineNotFoundException();
        }

        MachineUsabilityService machineUsabilityService = new MachineUsabilityService(machine);

        if (machineUsabilityService.isInMaintenance()) {
            throw new MachineAlreadyInMaintenanceException();
        } else if (!machineUsabilityService.isAvailable()) {
            throw new MachineNotAvailableException();
        }

        MachineUse maintenanceUse = new MachineUse();
        maintenanceUse.setResult(MachineUseResult.IN_MAINTENANCE.getCodeString());
        maintenanceUse.setTimestamp(DateHelper.getUruguayanCurrentDateTime().toDate());
        maintenanceUse.setMachine(machine);
        maintenanceUse.setBuilding(machine.getBuilding());
        maintenanceUse.save();
    }

    public void interruptMachineMaintenance(int machineId) throws APIException {
        Machine machine = new MachineQuery().get(machineId);
        if (machine == null) {
            throw new MachineNotFoundException();
        }

        MachineUsabilityService machineUsabilityService = new MachineUsabilityService(machine);
        if (!machineUsabilityService.isInMaintenance()) {
            throw new MachineNotInMaintenanceException();
        }

        MachineUse maintenanceInterruptedUse = new MachineUse();
        maintenanceInterruptedUse.setResult(
            MachineUseResult.MAINTENANCE_INTERRUPTED.getCodeString()
        );
        maintenanceInterruptedUse.setTimestamp(DateHelper.getUruguayanCurrentDateTime().toDate());
        maintenanceInterruptedUse.setMachine(machine);
        maintenanceInterruptedUse.setBuilding(machine.getBuilding());
        maintenanceInterruptedUse.save();
    }
}
