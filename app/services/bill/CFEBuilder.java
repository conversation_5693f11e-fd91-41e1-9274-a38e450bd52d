package services.bill;

import domains.billing.dto.Branch;
import domains.billing.invoicing.sicfe.types.SicfeInvoicingServiceConfig;
import domains.exchange_rate.services.exceptions.ExchangeRateNotFoundException;
import global.APIException;
import java.text.SimpleDateFormat;
import java.util.*;
import models.Bill.BillType;
import models.Currency;
import models.ExchangeRate;
import org.apache.commons.lang3.StringUtils;
import queries.exchange_rate.ExchangeRateQuery;

public class CFEBuilder {

    private static double memoRateUSD = 0.0;
    private static String memoDateUSD = StringUtils.EMPTY;

    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
    private CFERecipient recipient;
    private CFE bill;
    private boolean isExportation = false;
    private SicfeInvoicingServiceConfig config;

    private enum CFEFormat {
        E_TICKET("101"),
        NOTA_DE_CREDITO_E_TICKET("102"),
        NOTA_DE_DEBITO_E_TICKET("103"),
        E_FACTURA("111"),
        NOTA_DE_CREDITO_E_FACTURA("112"),
        NOTA_DE_DEBITO_E_FACTURA("113"),
        E_FACTURA_EXPORTATION("121");

        private final String cfeCode;

        CFEFormat(String cfeCode) {
            this.cfeCode = cfeCode;
        }

        public String getCfeCode() {
            return this.cfeCode;
        }
    }

    public CFEBuilder(CFE bill, SicfeInvoicingServiceConfig config) {
        this.recipient = bill.getRecipient();
        this.bill = bill;
        this.config = config;
    }

    public String build() throws Exception {
        StringBuilder detail = new StringBuilder();
        HashMap<String, List<CFEItem>> summaryMap = new HashMap<>();
        int i = 1;

        for (CFEItem item : bill.getItems()) {
            String key = String.format("%s : %s", item.getNomItem(), item.getPrecioUnitario());

            if (summaryMap.containsKey(key)) {
                summaryMap.get(key).add(item);
            } else {
                List<CFEItem> list = new ArrayList<>();
                list.add(item);
                summaryMap.put(key, list);
            }
        }

        for (String key : summaryMap.keySet()) {
            double itemAmount = 0;
            int quantity = 0;
            CFEItem item = summaryMap.get(key).get(0);

            for (CFEItem it : summaryMap.get(key)) {
                quantity += it.getCantidad();
                itemAmount += it.getCantidad() * it.getPrecioUnitario();
            }

            int indFact = item.getIndDet();
            // Para los CFEs de cobranza: los ítems de dicho CFE deberán tener como
            // indicador de facturación 6 - montos no facturable.
            if (bill.isCollectionReceipt()) indFact = 6;
            // Para los CFEs de cobranza: En caso de tener que anular un documento de
            // recibo, se utiliza eFactura/eTicket según corresponda con indicador de
            // facturación 7 - montos no facturable negativo.
            else if (bill.isCancelCollectionReceipt()) indFact = 7;

            double precioUnitario = item.getPrecioUnitario();
            if (isBillTypeExportation()) {
                precioUnitario = precioUnitario * 1.22;
                itemAmount = itemAmount * 1.22;
                indFact = 10;
            }

            String itemString =
                "<nsAd:Item>" +
                "<nsAd:NroLinDet>" +
                i +
                "</nsAd:NroLinDet>" +
                "<nsAd:IndFact>" +
                indFact +
                "</nsAd:IndFact>" +
                "<nsAd:NomItem>" +
                item.getNomItem() +
                "</nsAd:NomItem>" +
                "<nsAd:Cantidad>" +
                quantity +
                "</nsAd:Cantidad>" +
                "<nsAd:UniMed>" +
                item.getUnidadMedida() +
                "</nsAd:UniMed>" +
                "<nsAd:PrecioUnitario>" +
                num(precioUnitario) +
                "</nsAd:PrecioUnitario>" +
                "<nsAd:DescuentoPct>0</nsAd:DescuentoPct>" +
                "<nsAd:DescuentoMonto>0.00</nsAd:DescuentoMonto>" +
                "<nsAd:RecargoPct>0.00</nsAd:RecargoPct>" +
                "<nsAd:RecargoMnt>0.00</nsAd:RecargoMnt>" +
                "<nsAd:MontoItem>" +
                num(itemAmount) +
                "</nsAd:MontoItem>" +
                "</nsAd:Item>";

            detail.append(itemString);
            i++;
        }

        return (
            "<nsAdenda:CFE_Adenda xmlns:nsAdenda=\"http://cfe.dgi.gub.uy\">" +
            "<nsAd:CFE xmlns:nsAd=\"http://cfe.dgi.gub.uy\" version=\"1.0\">" +
            "<nsAd:" +
            getRootTag() +
            ">" +
            "<nsAd:Encabezado>" +
            getIdDocTag() +
            "<nsAd:Emisor>" +
            getBranchXML() +
            "</nsAd:Emisor>" +
            "<nsAd:Receptor>" +
            getDocRecep(recipient.getTipoDoc()) +
            "<nsAd:RznSocRecep>" +
            recipient.getBillingName() +
            "</nsAd:RznSocRecep>" +
            "<nsAd:DirRecep>" +
            recipient.getDireccion() +
            "</nsAd:DirRecep>" +
            "<nsAd:CiudadRecep>" +
            recipient.getCiudad() +
            "</nsAd:CiudadRecep>" +
            "<nsAd:DeptoRecep>" +
            recipient.getDepartamento() +
            "</nsAd:DeptoRecep>" +
            "<nsAd:PaisRecep>" +
            recipient.getPais() +
            "</nsAd:PaisRecep>" +
            "<nsAd:CP>0</nsAd:CP>" +
            "<nsAd:InfoAdicional></nsAd:InfoAdicional>" +
            "</nsAd:Receptor>" +
            "<nsAd:Totales>" +
            "<nsAd:TpoMoneda>" +
            bill.getTipoMoneda() +
            "</nsAd:TpoMoneda>" +
            (
                bill.getTipoMoneda().equals("USD")
                    ? "<nsAd:TpoCambio>" + currency(getUSDExchangeRate()) + "</nsAd:TpoCambio>"
                    : ""
            ) +
            (isBillTypeExportation() ? "" : getMontoNoGravado()) +
            (
                isBillTypeExportation()
                    ? "<nsAd:MntExpoyAsim>" + num(bill.getMontoTotal()) + "</nsAd:MntExpoyAsim>"
                    : ""
            ) +
            (
                isBillTypeExportation()
                    ? ""
                    : "<nsAd:MntImpuestoPerc>0.00</nsAd:MntImpuestoPerc>" +
                    "<nsAd:MntIVaenSusp>0.00</nsAd:MntIVaenSusp>" +
                    "<nsAd:MntNetoIvaTasaMin>0.00</nsAd:MntNetoIvaTasaMin>"
            ) +
            (isBillTypeExportation() ? "" : montoNetoIVATasaBasica()) +
            (
                isBillTypeExportation()
                    ? ""
                    : "<nsAd:MntNetoIVAOtra>0.00</nsAd:MntNetoIVAOtra>" +
                    "<nsAd:IVATasaMin>10.00</nsAd:IVATasaMin>" +
                    "<nsAd:IVATasaBasica>22.00</nsAd:IVATasaBasica>" +
                    "<nsAd:MntIVATasaMin>0.00</nsAd:MntIVATasaMin>" +
                    montoIVATasaBasica()
            ) +
            (isBillTypeExportation() ? "" : "<nsAd:MntIVAOtra>0.00</nsAd:MntIVAOtra>") +
            "<nsAd:MntTotal>" +
            num(bill.getMontoTotal()) +
            "</nsAd:MntTotal>" +
            (isBillTypeExportation() ? "" : "<nsAd:MntTotRetenido>0.00</nsAd:MntTotRetenido>") +
            "<nsAd:CantLinDet>" +
            (i - 1) +
            "</nsAd:CantLinDet>" +
            "<nsAd:MontoNF>0.00</nsAd:MontoNF>" +
            "<nsAd:MntPagar>" +
            num(bill.getMontoTotal()) +
            "</nsAd:MntPagar>" +
            "</nsAd:Totales>" +
            "</nsAd:Encabezado>" +
            "<nsAd:Detalle>" +
            detail.toString() +
            "</nsAd:Detalle>" +
            getReferenceTag((i - 1)) +
            "<nsAd:CAEData>" +
            "<nsAd:CAE_ID></nsAd:CAE_ID>" +
            "<nsAd:DNro></nsAd:DNro>" +
            "<nsAd:HNro></nsAd:HNro>" +
            "<nsAd:FecVenc></nsAd:FecVenc>" +
            "</nsAd:CAEData>" +
            "</nsAd:" +
            getRootTag() +
            ">" +
            "</nsAd:CFE>" +
            "</nsAdenda:CFE_Adenda>"
        );
    }

    private String getDocRecep(int docType) {
        if (docType < 0) {
            return StringUtils.EMPTY;
        }

        String docRecep =
            (
                "<nsAd:TipoDocRecep>" +
                recipient.getTipoDoc() +
                "</nsAd:TipoDocRecep>" +
                "<nsAd:CodPaisRecep>" +
                recipient.getCodPais() +
                "</nsAd:CodPaisRecep>"
            );
        if (docType < 4) {
            docRecep += "<nsAd:DocRecep>" + recipient.getDoc() + "</nsAd:DocRecep>";
        } else if (docType == 4) {
            // foreigners
            docRecep += "<nsAd:DocRecepExt>" + recipient.getDoc() + "</nsAd:DocRecepExt>";
        }

        return docRecep;
    }

    private double getUSDExchangeRate() throws APIException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (
            memoRateUSD == 0.0 ||
            StringUtils.isBlank(memoDateUSD) ||
            !sdf.format(new Date()).equals(memoDateUSD)
        ) {
            ExchangeRateQuery query = new ExchangeRateQuery()
                .filterByCurrencyIsoCode(Currency.USD.getIsoCode());
            ExchangeRate exchangeRate = query.single();

            if (exchangeRate == null) {
                // Do we want to continue the billing process if we don't have a exchange rate
                // for USD?
                // This can be bypassed updating the exchange rate record in the DB
                play.Logger.info("No se pudo obtener la cotización USD");
                throw new ExchangeRateNotFoundException();
            }

            memoRateUSD = exchangeRate.getValue();
            memoDateUSD = sdf.format(new Date());
        }

        play.Logger.info("Cotización USD utilizada para la operacion: " + memoRateUSD);

        return memoRateUSD;
    }

    private String getMontoNoGravado() {
        return (
            "<nsAd:MntNoGrv>" +
            (bill.isTaxFree() ? num(bill.getMontoTotal()) : "0.00") +
            "</nsAd:MntNoGrv>"
        );
    }

    private String montoNetoIVATasaBasica() {
        return (
            "<nsAd:MntNetoIVATasaBasica>" +
            (bill.isTaxFree() ? "0.00" : num(bill.getMontoNetoIvaTasaBasica())) +
            "</nsAd:MntNetoIVATasaBasica>"
        );
    }

    private String montoIVATasaBasica() {
        return (
            "<nsAd:MntIVATasaBasica>" +
            (bill.isTaxFree() ? "0.00" : num(bill.getMontoIvaTasaBasica())) +
            "</nsAd:MntIVATasaBasica>"
        );
    }

    // public double isMoreThan5kUI() throws Exception {
    // double amountInUYU = bill.getTipoMoneda().equals("UYU")
    // ? bill.getMontoTotal()
    // : bill.getMontoTotal() * getUSDExchangeRate();
    //
    // return amountInUYU > this.getUIExchangeRate() * MAX_UI_AMOUNT;
    // }

    private String getTipoCFE() throws Exception {
        if (isBillTypeExportation()) {
            return CFEFormat.E_FACTURA_EXPORTATION.getCfeCode();
        }

        if (bill.isCreditNote()) {
            if (bill.getRecipient().getTipoDoc() == 2) {
                return CFEFormat.NOTA_DE_CREDITO_E_FACTURA.getCfeCode();
            } else {
                return CFEFormat.NOTA_DE_CREDITO_E_TICKET.getCfeCode();
            }
        } else if (bill.isDebitNote()) {
            if (bill.getRecipient().getTipoDoc() == 2) {
                return CFEFormat.NOTA_DE_DEBITO_E_FACTURA.getCfeCode();
            } else {
                return CFEFormat.NOTA_DE_DEBITO_E_TICKET.getCfeCode();
            }
        } else {
            if (bill.getRecipient().getTipoDoc() == 2) {
                return CFEFormat.E_FACTURA.getCfeCode();
            } else {
                return CFEFormat.E_TICKET.getCfeCode();
            }
        }
    }

    private String getRootTag() throws Exception {
        switch (getTipoCFE()) {
            case "121":
                return "eFact_Exp";
            case "111":
            case "112":
            case "113":
                return "eFact";
            default:
                return "eTck";
        }
    }

    private String num(double num) {
        return String.format(Locale.US, "%.2f", num);
    }

    private String currency(double num) {
        return String.format(Locale.US, "%.3f", num);
    }

    private String getReferenceTag(int lines) {
        if (
            (
                !bill.isCreditNote() &&
                !bill.isDebitNote() &&
                !bill.isCollectionReceipt() &&
                !bill.isCancelCollectionReceipt()
            ) ||
            bill.getBillReference() == null
        ) return "";

        String reason = "";
        if (bill.isCollectionReceipt()) {
            reason = "Cobranza propia";
        } else if (bill.isCancelCollectionReceipt()) {
            reason = "Cobranza propia cancelada";
        } else if (bill.getCreditNoteReason() != null) {
            reason = bill.getCreditNoteReason();
        }

        return (
            " <nsAd:Referencia>" +
            "<nsAd:Referencia>" +
            "<nsAd:NroLinRef>" +
            lines +
            "</nsAd:NroLinRef>" +
            "<nsAd:TpoDocRef>" +
            bill.getBillReference().getTipoDoc() +
            "</nsAd:TpoDocRef>" +
            "<nsAd:Serie>" +
            bill.getBillReference().getSerie() +
            "</nsAd:Serie>" +
            "<nsAd:NroCFERef>" +
            bill.getBillReference().getNumber() +
            "</nsAd:NroCFERef>" +
            "<nsAd:RazonRef>" +
            reason +
            "</nsAd:RazonRef>" +
            "<nsAd:FechaCFEref>" +
            dateFormat.format(bill.getBillReference().getTimestamp()) +
            "</nsAd:FechaCFEref>" +
            "</nsAd:Referencia>" +
            "</nsAd:Referencia>"
        );
    }

    private String getIdDocTag() throws Exception {
        return (
            "<nsAd:IdDoc>" +
            "<nsAd:TipoCFE>" +
            getTipoCFE() +
            "</nsAd:TipoCFE>" +
            (
                bill.getSerie() != null
                    ? "<nsAd:Serie>" + bill.getSerie() + "</nsAd:Serie>"
                    : "<nsAd:Serie></nsAd:Serie>"
            ) +
            (
                bill.getNumber() != null
                    ? "<nsAd:Nro>" + bill.getNumber().toString() + "</nsAd:Nro>"
                    : "<nsAd:Nro>0</nsAd:Nro>"
            ) +
            "<nsAd:FchEmis>" +
            dateFormat.format(bill.getTimestamp()) +
            "</nsAd:FchEmis>" +
            (
                bill.getPeriodoDesde() != null
                    ? "<nsAd:PeriodoDesde>" +
                    dateFormat.format(bill.getPeriodoDesde()) +
                    "</nsAd:PeriodoDesde>"
                    : ""
            ) +
            (
                bill.getPeriodoHasta() != null
                    ? "<nsAd:PeriodoHasta>" +
                    dateFormat.format(bill.getPeriodoHasta()) +
                    "</nsAd:PeriodoHasta>"
                    : ""
            ) +
            "<nsAd:FmaPago>" +
            bill.getFormaPago() +
            "</nsAd:FmaPago>" +
            (
                isBillTypeExportation()
                    ? "<nsAd:ClauVenta>FOB</nsAd:ClauVenta>" +
                    "<nsAd:ModVenta>1</nsAd:ModVenta>" +
                    "<nsAd:ViaTransp>3</nsAd:ViaTransp>"
                    : ""
            ) +
            (
                bill.isCollectionReceipt() || bill.isCancelCollectionReceipt()
                    ? "<nsAd:IndCobPropia>1</nsAd:IndCobPropia>" // Para los CFEs de cobranza: DGI agregó el
                    // indicador "Indicador Cobranza propia" en la zona
                    // de Identificación del comprobante, que se deberá
                    // enviar con valor 1
                    : ""
            ) +
            "</nsAd:IdDoc>"
        );
    }

    /*
     * Branch closureData section
     */

    /**
     * (5) Default value for non specified stores
     */
    private static final int MAIN_BRANCH_NUMBER = 5;

    private String getBranchXML() {
        return (
            "<nsAd:RUCEmisor>" +
            this.config.senderRuc +
            "</nsAd:RUCEmisor>" +
            "<nsAd:RznSoc>" +
            this.config.companyName +
            "</nsAd:RznSoc>" +
            "<nsAd:NomComercial>" +
            this.config.commercialName +
            "</nsAd:NomComercial>" +
            "<nsAd:GiroEmis>1</nsAd:GiroEmis>" +
            "<nsAd:Telefono>" +
            this.config.phone +
            "</nsAd:Telefono>" +
            "<nsAd:CorreoEmisor>" +
            this.config.email +
            "</nsAd:CorreoEmisor>" +
            "<nsAd:EmiSucursal>" +
            getBranchType() +
            "</nsAd:EmiSucursal>" +
            "<nsAd:CdgDGISucur>" +
            getBranchNumber() +
            "</nsAd:CdgDGISucur>" +
            "<nsAd:DomFiscal>" +
            getBranchAddress() +
            "</nsAd:DomFiscal>" +
            "<nsAd:Ciudad>" +
            getBranchCity() +
            "</nsAd:Ciudad>" +
            "<nsAd:Departamento>" +
            getBranchState() +
            "</nsAd:Departamento>"
        );
    }

    /**
     * Get the value for the field `CdgDGISucur`,
     * it identifies a LAVOMAT branch, aka Laundromats
     */
    public int getBranchNumber() {
        Branch branch = this.bill.getBranch();
        if (branch != null && branch.getNumber() != 0) {
            return branch.getNumber();
        }

        return MAIN_BRANCH_NUMBER;
    }

    /**
     * Get the value for the field `EmiSucursal`,
     * it identifies the branch type, if it is the main one or not
     */
    public String getBranchType() {
        return getBranchNumber() == MAIN_BRANCH_NUMBER ? "Principal" : "Sucursal";
    }

    /**
     * Get the value for the field `DomFiscal`,
     * returns the branch address
     */
    public String getBranchAddress() {
        Branch branch = this.bill.getBranch();
        if (branch != null && StringUtils.isNotBlank(branch.getAddress())) {
            return branch.getAddress();
        }

        return "Avenida Brasil 3072 local 21";
    }

    /**
     * Get the value for the field `Ciudad`,
     * returns the branch city
     */
    public String getBranchCity() {
        Branch branch = this.bill.getBranch();
        if (branch != null && StringUtils.isNotBlank(branch.getCity())) {
            return branch.getCity();
        }

        return "Montevideo";
    }

    /**
     * Get the value for the field `Departamento`,
     * returns the branch state, aka "departamento"
     */
    public String getBranchState() {
        Branch branch = this.bill.getBranch();
        if (branch != null && StringUtils.isNotBlank(branch.getState())) {
            return branch.getState();
        }

        return "Montevideo";
    }

    private boolean isBillTypeExportation() {
        return this.bill.getBillType().equals(BillType.EFACTEXPORTATION);
    }
}
