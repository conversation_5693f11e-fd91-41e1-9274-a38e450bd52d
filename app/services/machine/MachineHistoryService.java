package services.machine;

import models.Machine;
import models.MachineHistoryRecord;

public class MachineHistoryService {

    public boolean shouldRegisterRecord(
        Machine machine,
        String newDescription,
        String newSerialNumber,
        String newReference,
        int newSortIndex
    ) {
        return (
            (newDescription != null && !newDescription.equals(machine.getDescription())) ||
            (newSerialNumber != null && !newSerialNumber.equals(machine.getSerialNumber())) ||
            (newReference != null && !newReference.equals(machine.getReference())) ||
            (machine.getSortIndex() != newSortIndex && newSortIndex >= 0)
        );
    }

    public void registerMachineRecord(Machine machine) {
        new MachineHistoryRecord(machine).save();
    }
}
