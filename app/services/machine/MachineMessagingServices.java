package services.machine;

import global.APIException;
import global.APIException.APIErrors;
import utils.LMKafkaProducer;

public class MachineMessagingServices {

    public static void produceBrokerMessage(final int sendMessageCount) throws Exception {
        try {
            LMKafkaProducer.runProducer(5);
        } catch (Exception e) {
            throw APIException
                .raise(APIErrors.MACHINE_ACTIVATION_FAILED)
                .setDetailMessage("Machine activation process failed");
        }
    }
}
