package services.accounts;

import java.util.Date;
import models.Account;
import models.User;

public class AccountFactory extends services.BaseService {

    protected Account account;

    public AccountFactory() {}

    public AccountFactory(Account account) {
        this.account = account;
    }

    public AccountFactory build(User user) {
        this.account = new Account();

        this.account.setOwner(user);
        this.account.setCreationTimestamp(new Date());

        return this;
    }

    public AccountFactory validate() {
        this.account.setValidated(true);
        this.account.setValidationDate(new Date());

        return this;
    }

    public AccountFactory waitForValidation() {
        this.account.waitForValidation();
        this.account.setValidated(false);

        return this;
    }

    public Account create() {
        this.account.save();
        return this.account;
    }

    public Account update() {
        this.account.update();
        return this.account;
    }
}
