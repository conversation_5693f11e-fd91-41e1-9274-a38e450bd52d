package services.administration;

import domains.billing.closure.CloserDirector;
import domains.billing.closure.ValidatorDirector;
import domains.billing.closure.exceptions.BuildingClosureException;
import domains.billing.closure.exceptions.ClosureValidationException;
import global.APIException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import models.Administration;
import models.Attachment;
import models.Building;
import models.ClosureRecord;
import org.joda.time.DateTime;
import play.libs.F;
import services.BaseService;
import utils.ApplicationConfiguration;
import utils.email.EmailService;

public class ClosureService extends BaseService {

    private static final int BACKWARD_DAYS_FOR_BILLING = 60;

    @Override
    protected String getLoggerProviderName() {
        return "Automatic Administration Closure";
    }

    /**
     * Running automatic closure for all the administrations
     */
    public void runAll() {
        logger("Starting multiple mode...");
        List<Administration> adminsToClose = getAdministrationsToClose(new Date());

        logger("Got {} administrations to close.", adminsToClose.size());
        for (Administration admin : adminsToClose) {
            logger("Processing \"{}\" administration.", admin.getName());
            processAdministrationClosure(admin, null);
        }
    }

    /**
     * Running automatic closure for a given administration
     */
    public void runIndividually(int administrationId, String closeAt) throws APIException {
        logger("Starting single mode...");
        logger("Getting administration id \"{}\" to close.", administrationId);

        Administration adminToClose = Administration.findById(administrationId);

        Date until = null;
        if (closeAt == null) {
            // Compute the past closure date for this admin
            int closureDay = adminToClose.getClosureDay();

            if (closureDay == 0) {
                logger(
                    "ERROR - Thrown error during Administration \"{}\" processing - \"closure day\" is zero.",
                    adminToClose.getName()
                );
                throw APIException
                    .raise(APIException.APIErrors.BAD_DATES)
                    .setDetailMessage("\"closure day\" is zero.");
            }

            DateTime today = DateTime.now();
            int dayOfMonth = today.getDayOfMonth();
            if (closureDay <= dayOfMonth) {
                until = new DateTime().withDayOfMonth(closureDay).toDate();
            } else {
                int maxDaysOfMonth = today.dayOfMonth().getMaximumValue();
                closureDay = Math.min(maxDaysOfMonth, closureDay);
                until = new DateTime().withDayOfMonth(closureDay).minusMonths(1).toDate();
            }
        } else {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            try {
                until = sdf.parse(closeAt);
            } catch (ParseException e) {
                logger(
                    "ERROR - Thrown error during Administration \"{}\" processing - parsing \"until\" date: \"{}\".",
                    adminToClose.getName(),
                    closeAt
                );
                e.printStackTrace();
                throw APIException.raise(APIException.APIErrors.BAD_DATE_FORMAT);
            }
        }

        logger("Processing \"{}\" administration.", adminToClose.getName());
        processAdministrationClosure(adminToClose, until);
    }

    /**
     * Given a date returns the list of administration that are ready to close.
     */
    private List<Administration> getAdministrationsToClose(Date date) {
        List<Administration> administrations = Administration.findAll();
        List<Administration> result = new ArrayList<>();

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int currentDayOfMonth = cal.get(Calendar.DAY_OF_MONTH);
        logger("Getting administrations which close at {} day of the month.", currentDayOfMonth);

        for (Administration a : administrations) {
            if (a.getClosureDay() == currentDayOfMonth) result.add(a);
        }
        return result;
    }

    /**
     * Iterates over all the buildings of the given Administration and closes them
     * if possible.
     */
    private void processAdministrationClosure(Administration administration, Date until) {
        Date closureDayAtMidnight = new DateTime(until != null ? until : new Date())
            .minusDays(1)
            .millisOfDay()
            .withMaximumValue()
            .withMillisOfSecond(0)
            .toDate();

        Date previousClosureDayAtMidnight = new DateTime(until != null ? until : new Date())
            .minusMonths(1)
            .millisOfDay()
            .withMinimumValue()
            .toDate();

        List<Integer> buildingIds = new ArrayList<>();
        for (Building building : administration.getBuildings()) {
            if (!building.isIncludedInClosure()) {
                logger("Building is not included on closure \"{}\".", building.getName());
                continue;
            }

            buildingIds.add(building.getId());
        }
        logger("Got {} buildings to process.", buildingIds.size());

        List<Attachment> filesToSend = new ArrayList<>();
        for (Integer buildingId : buildingIds) {
            try {
                List<Attachment> files = processBuildingClosure(
                    buildingId,
                    closureDayAtMidnight,
                    previousClosureDayAtMidnight,
                    until
                );

                if (files == null || files.isEmpty()) continue;

                filesToSend.addAll(files);
            } catch (BuildingClosureException | ClosureValidationException e) {
                // jump to next building
                e.printStackTrace();
            }
        }

        if (ApplicationConfiguration.isLocal()) {
            try {
                for (Attachment attachment : filesToSend) {
                    attachment.saveFileToDisk();
                }
            } catch (Exception ex) {
                // Do nothing
            }
        }

        try {
            sendAdministrationReport(administration, filesToSend);
        } catch (BuildingClosureException e) {
            logger(
                "ERROR - Thrown error during Administration \"{}\" sending report, it was not sent - error: {} - message: {}.",
                administration.getName(),
                e.getError(),
                e.getMessage()
            );
        } catch (Throwable e) {
            logger(
                "ERROR - Thrown error during Administration \"{}\" sending report, it was not sent - error: {} - message: {}.",
                administration.getName(),
                "UNEXPECTED",
                e.getMessage()
            );
            e.printStackTrace();
        } finally {
            logger("Finished for \"{}\".", administration.getName());
        }
    }

    /**
     * Given a building executes operations required to close it.
     *
     * @return the files to be sent to administration if it applies.
     * @throws BuildingClosureException when something happens that cancels the
     *                                  closure process.
     */
    private List<Attachment> processBuildingClosure(
        int buildingId,
        Date closureDayAtMidnight,
        Date previousClosureDayAtMidnight,
        Date until
    ) throws BuildingClosureException, ClosureValidationException {
        // how much is ClosureRecord trustable to log events?
        // if the building is HYBRID it could be overridden
        // TODO: create two recording - pre & post paid ?

        F.Function0<List<Attachment>> function = () -> {
            Building building = Building.findById(buildingId);
            ClosureRecord recording = new ClosureRecord(building);

            logger(
                "Processing \"{}\" building. From: {} - To: {}",
                building.getName(),
                previousClosureDayAtMidnight.toString(),
                closureDayAtMidnight.toString()
            );

            if (!isBuildingCloseable(building, until)) {
                recording.fail("Building is not closeable");
                logger("Building \"{}\" is not closeable.", building.getName());
                return null;
            }

            logger("Building \"{}\" is closeable.", building.getName());

            if (wasBuildingClosedAt(building, closureDayAtMidnight)) {
                recording.fail("Building already closed for this period");
                logger("Building \"{}\" is already closed for this period.", building.getName());
                return null;
            }

            ValidatorDirector validatorDirector = new ValidatorDirector(
                building,
                closureDayAtMidnight
            );
            validatorDirector.validate();

            CloserDirector closerDirector = new CloserDirector(
                building,
                closureDayAtMidnight,
                previousClosureDayAtMidnight,
                recording
            );
            closerDirector.close();
            List<Attachment> files = new ArrayList<>(closerDirector.getAttachments());
            closerDirector.clean();

            return files;
        };

        try {
            // How the rollback works
            // https://github.com/playframework/playframework/blob/b3d1aec6f221b6109384a901455bf62108d0ce17/framework/src/play-java-jpa/src/main/java/play/db/jpa/DefaultJPAApi.java#L118
            return play.db.jpa.JPA.withTransaction(function);
        } catch (BuildingClosureException | ClosureValidationException e) {
            loggerError(e.getMessage() + " building: {}", buildingId);
            // TODO: find out how to track this case since the save action has to occur
            // inside the transaction
            e.printStackTrace();
            throw e;
        } catch (Throwable e) {
            loggerError(e.getMessage() + " building: {}", buildingId);
            // TODO: find out how to track this case since the save action has to occur
            // inside the transaction
            e.printStackTrace();
        }

        return Collections.emptyList();
    }

    /**
     * Given an administration sends the report files of its buildings
     *
     * @throws BuildingClosureException RENDERING_VIEW, SENDING_EMAIL
     */
    private void sendAdministrationReport(
        Administration administration,
        List<Attachment> filesToSend
    ) throws BuildingClosureException {
        logger(
            "Got {} files of Administration \"{}\" to send.",
            filesToSend.size(),
            administration.getName()
        );

        if (filesToSend.isEmpty()) {
            logger(
                "ERROR - The Administration \"{}\" has not generated files to send.",
                administration.getName()
            );

            return;
        }

        logger("Sending email to: {}.", administration.getContact());

        String body = "";
        try {
            body = views.html.postpaidReport.render().body();
        } catch (Throwable e) {
            e.printStackTrace();

            throw BuildingClosureException.raise(BuildingClosureException.Errors.RENDERING_VIEW, e);
        }

        try {
            EmailService.send(
                administration.getContact(),
                "Cierre Mensual LAVOMAT",
                body,
                filesToSend
            );
        } catch (Exception e) {
            e.printStackTrace();

            throw BuildingClosureException.raise(BuildingClosureException.Errors.SENDING_EMAIL, e);
        }
    }

    /* HELPER METHODS */

    /**
     * Evaluates whether the building can be closed according to the following
     * rules:
     *
     * 1. Closure date is yesterday
     * 2. Last alive is greater than today at 00:00 3.
     * There is not pending uses.
     *
     * @param building The building to evaluate
     * @param at       Given date to evaluate closure
     * @return <code>true</code> if the building can be closed, <code>false</code>
     *         otherwise
     */
    private boolean isBuildingCloseable(Building building, Date at) {
        if (building.getOldestLastAlive() == null) {
            return false;
        }

        Date todayAtMidnight = new DateTime(at != null ? at : new Date())
            .withTimeAtStartOfDay()
            .toDate();

        return (
            building.getTotalPendingUses() == 0 &&
            building.getOldestLastAlive().getTime() > todayAtMidnight.getTime()
        );
    }

    private boolean wasBuildingClosedAt(Building building, Date at) {
        boolean hasLastBillingDate = building.getLastBillingDate() != null;
        boolean alreadyClosed =
            hasLastBillingDate && building.getLastBillingDate().getTime() >= at.getTime();
        return hasLastBillingDate && alreadyClosed;
    }
}
