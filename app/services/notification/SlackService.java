package services.notification;

import global.APIException;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONException;
import org.json.JSONObject;
import services.BaseService;
import utils.ApplicationConfiguration;
import utils.HttpRequest;

public class SlackService extends BaseService {

    /**
     * how to create a new hook for a new channel
     * 1. go to https://api.slack.com/apps
     * 2. choose "LM-API" app
     * 3. go to "Incoming Webhooks"
     * 4. click on "Add New Webhook to Workspace"
     */

    private static final String TESTING_HOOK =
        "*******************************************************************************";
    private static final String ALERTS_TOTEM_HOOK =
        "*******************************************************************************";

    public enum Channel {
        TESTING,
        ALERTS_TOTEM,
    }

    public void notifyChannel(Channel channel, String message) throws APIException {
        String hook = this.getHookUrl(channel);
        this.send(hook, message);
    }

    private String getHookUrl(Channel channel) {
        switch (channel) {
            case TESTING:
                return TESTING_HOOK;
            case ALERTS_TOTEM:
                return ALERTS_TOTEM_HOOK;
            default:
                return StringUtils.EMPTY;
        }
    }

    private void send(String hook, String message) throws APIException {
        try {
            new HttpRequest(ApplicationConfiguration.isProd() ? hook : TESTING_HOOK)
                .post(new JSONObject().put("text", message));
        } catch (JSONException e) {
            loggerError("Error while creating body message. message: {}", message);
        }
    }
}
