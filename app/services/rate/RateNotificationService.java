package services.rate;

import static utils.DateHelper.getMonthName;

import global.APIException;
import global.exceptions.InvalidRateEventException;
import global.exceptions.RateNotFoundException;
import java.util.Arrays;
import java.util.List;
import models.Building;
import models.Card;
import models.Rate;
import models.RateEvent;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import queries.buildings.BuildingQuery;
import queries.rates.RateEventsForBuildingAdminNotificationQuery;
import queries.rates.RateQuery;
import queries.rates.UpcomingRatesToExpire;
import services.BaseService;
import utils.ApplicationConfiguration;
import utils.email.EmailService;

public class RateNotificationService extends BaseService {

    private static final int PRIOR_DAYS_PER_NEW_RATE_NOTIFICATION = 40;
    private static final int PRIOR_DAYS_PER_RATE_EXPIRATION_NOTIFICATION = 50;

    public void sendBuildingAdminNewRatePricesEmail(int rateId) throws APIException {
        Rate rate = new RateQuery().get(rateId);
        if (rate == null) {
            throw new RateNotFoundException();
        }

        RateEvent lastRateEvent = rate.getLastRateEvent();
        if (lastRateEvent.isCurrentRateEvent() || lastRateEvent.isPastRateEvent()) {
            // Only notify upcoming rate events, not currents
            throw new InvalidRateEventException();
        }

        this.notifyBuildingsOfUpcomingNewRate(lastRateEvent, rate);
    }

    public void sendBuildingAdminsNewRatePricesEmail() {
        RateEventsForBuildingAdminNotificationQuery query = new RateEventsForBuildingAdminNotificationQuery();
        List<RateEvent> upcomingUpdatingRateEvents = query.getRateEventsForBuildingAdminNotification(
            PRIOR_DAYS_PER_NEW_RATE_NOTIFICATION
        );
        if (upcomingUpdatingRateEvents == null || upcomingUpdatingRateEvents.isEmpty()) {
            logger("No new rates to notify");
            return;
        }

        for (RateEvent rateEvent : upcomingUpdatingRateEvents) {
            Rate rate = rateEvent.getRate();

            this.notifyBuildingsOfUpcomingNewRate(rateEvent, rate);
        }
    }

    public void sendLavomatAdminUpcomingRatesExpirationEmail() {
        UpcomingRatesToExpire query = new UpcomingRatesToExpire();
        List<Rate> upcomingRatesToExpire = query.getUpcomingRatesToExpire(
            PRIOR_DAYS_PER_RATE_EXPIRATION_NOTIFICATION
        );
        if (upcomingRatesToExpire == null || upcomingRatesToExpire.isEmpty()) {
            logger("No rates about to expire");
            return;
        }

        for (Rate rate : upcomingRatesToExpire) {
            String body = views.html.upcomingRateExpiration
                .render(rate.getName(), String.valueOf(rate.getId()))
                .body();

            try {
                EmailService.sendAsync(
                    this.getRecipients(rate),
                    "Próximo vencimiento de Rate",
                    body
                );

                rate.increaseEmailNotificationCountToLavomat();
                rate.update();
            } catch (Exception e) {
                loggerErrorWithException(
                    "Error while sending upcoming rate expiration email | Rate " + rate.getId(),
                    e
                );
            }
        }
    }

    private void notifyBuildingsOfUpcomingNewRate(RateEvent rateEvent, Rate rate) {
        BuildingQuery buildingQuery = new BuildingQuery();
        List<Building> buildingsToNotify = buildingQuery
            .filterByRateId(rate.getId())
            .filterByContractTypes(
                Card.ContractType.POSTPAID,
                Card.ContractType.PREPAID,
                Card.ContractType.MIXED
            )
            .find();

        if (buildingsToNotify == null || buildingsToNotify.isEmpty()) {
            logger("No buildings for rate {}", rate.getId());
            return;
        }

        for (Building building : buildingsToNotify) {
            logger("Sending ratePriceUpdate | Building: {}", building.getName());

            String to = building.getAdministration() == null ||
                StringUtils.isBlank(building.getAdministration().getContact())
                ? ApplicationConfiguration.getLavomatAssistantEmail()
                : building.getAdministration().getContact();

            String subject = "Ajuste Anual LAVOMAT | " + building.getName();

            String body = views.html.ratePriceUpdate
                .render(
                    String.valueOf(new DateTime(rateEvent.getValidFrom()).dayOfMonth().get()),
                    String.valueOf(
                        getMonthName(new DateTime(rateEvent.getValidFrom()).monthOfYear().get())
                    ),
                    String.valueOf(new DateTime(rateEvent.getValidFrom()).year().get()),
                    String.valueOf(rate.getCurrentRateEvent().getPriceCustomer()),
                    String.valueOf(rateEvent.getPriceCustomer()),
                    String.valueOf(rateEvent.getPriceCompany()),
                    String.valueOf(rateEvent.getPriceCustomer() - rateEvent.getPriceCompany())
                )
                .body();

            try {
                EmailService.sendAsync(to, subject, body);
            } catch (Exception e) {
                loggerErrorWithException(
                    "Error while sending new rate email | Building " +
                    building.getId() +
                    " | RateEvent " +
                    rateEvent.getId(),
                    e
                );
            }
        }

        rateEvent.setNotifiedToBuildingAdmin(true);
        rateEvent.update();
    }

    private String getRecipients(Rate rate) {
        List<String> recipients = Arrays.asList(
            ApplicationConfiguration.getLavomatAdministrationEmail()
        );
        if (rate.getEmailNotificationCountToLavomat() >= 9) {
            recipients.add(ApplicationConfiguration.getLavomatCEOEmail());
            recipients.add(ApplicationConfiguration.getLavomatCFOEmail());
        }

        return EmailService.generateRecipientsList(recipients);
    }
}
