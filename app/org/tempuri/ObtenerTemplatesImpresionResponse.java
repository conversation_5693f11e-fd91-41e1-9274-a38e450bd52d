
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.ObtenerTemplateImpresionRespuesta;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="ObtenerTemplatesImpresionResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}ObtenerTemplateImpresionRespuesta" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "obtenerTemplatesImpresionResult" })
@XmlRootElement(name = "ObtenerTemplatesImpresionResponse")
public class ObtenerTemplatesImpresionResponse {

    @XmlElementRef(name = "ObtenerTemplatesImpresionResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<ObtenerTemplateImpresionRespuesta> obtenerTemplatesImpresionResult;

    /**
     * Gets the value of the obtenerTemplatesImpresionResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link ObtenerTemplateImpresionRespuesta }{@code >}
     * 
     */
    public JAXBElement<ObtenerTemplateImpresionRespuesta> getObtenerTemplatesImpresionResult() {
        return obtenerTemplatesImpresionResult;
    }

    /**
     * Sets the value of the obtenerTemplatesImpresionResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link ObtenerTemplateImpresionRespuesta }{@code >}
     * 
     */
    public void setObtenerTemplatesImpresionResult(JAXBElement<ObtenerTemplateImpresionRespuesta> value) {
        this.obtenerTemplatesImpresionResult = value;
    }

}
