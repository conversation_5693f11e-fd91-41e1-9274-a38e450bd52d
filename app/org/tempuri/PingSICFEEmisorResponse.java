
package org.tempuri;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.datacontract.schemas._2004._07.sicfecontract.SICFERespuestaPing;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType>
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="PingSICFEEmisorResult" type="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuestaPing" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "pingSICFEEmisorResult" })
@XmlRootElement(name = "PingSICFEEmisorResponse")
public class PingSICFEEmisorResponse {

    @XmlElementRef(name = "PingSICFEEmisorResult", namespace = "http://tempuri.org/", type = JAXBElement.class, required = false)
    protected JAXBElement<SICFERespuestaPing> pingSICFEEmisorResult;

    /**
     * Gets the value of the pingSICFEEmisorResult property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link SICFERespuestaPing }{@code >}
     * 
     */
    public JAXBElement<SICFERespuestaPing> getPingSICFEEmisorResult() {
        return pingSICFEEmisorResult;
    }

    /**
     * Sets the value of the pingSICFEEmisorResult property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link SICFERespuestaPing }{@code >}
     * 
     */
    public void setPingSICFEEmisorResult(JAXBElement<SICFERespuestaPing> value) {
        this.pingSICFEEmisorResult = value;
    }

}
