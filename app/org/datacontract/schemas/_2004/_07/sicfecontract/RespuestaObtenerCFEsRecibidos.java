
package org.datacontract.schemas._2004._07.sicfecontract;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElementRef;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for RespuestaObtenerCFEsRecibidos complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="RespuestaObtenerCFEsRecibidos">
 *   &lt;complexContent>
 *     &lt;extension base="{http://schemas.datacontract.org/2004/07/SICFEContract}SICFERespuesta">
 *       &lt;sequence>
 *         &lt;element name="CFEsRecibidos" type="{http://schemas.datacontract.org/2004/07/SICFEContract}ArrayOfCFERecibidoDTO" minOccurs="0"/>
 *       &lt;/sequence>
 *     &lt;/extension>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "RespuestaObtenerCFEsRecibidos", propOrder = { "cfEsRecibidos" })
public class RespuestaObtenerCFEsRecibidos extends SICFERespuesta {

    @XmlElementRef(name = "CFEsRecibidos", namespace = "http://schemas.datacontract.org/2004/07/SICFEContract", type = JAXBElement.class, required = false)
    protected JAXBElement<ArrayOfCFERecibidoDTO> cfEsRecibidos;

    /**
     * Gets the value of the cfEsRecibidos property.
     * 
     * @return possible object is {@link JAXBElement
     *         }{@code <}{@link ArrayOfCFERecibidoDTO }{@code >}
     * 
     */
    public JAXBElement<ArrayOfCFERecibidoDTO> getCFEsRecibidos() {
        return cfEsRecibidos;
    }

    /**
     * Sets the value of the cfEsRecibidos property.
     * 
     * @param value allowed object is {@link JAXBElement
     *              }{@code <}{@link ArrayOfCFERecibidoDTO }{@code >}
     * 
     */
    public void setCFEsRecibidos(JAXBElement<ArrayOfCFERecibidoDTO> value) {
        this.cfEsRecibidos = value;
    }

}
