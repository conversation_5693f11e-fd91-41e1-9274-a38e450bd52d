CREATE PROCEDURE FillPreventiveMaintenanceEntries()
BEGIN
    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Start', 'FillBuildingAverageUses');
    CALL FillBuildingAverageUses();
    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Finish', 'FillBuildingAverageUses');

    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Start', 'FillPreventiveBaseMaintenanceBuildingEntry');
    CALL FillPreventiveBaseMaintenanceBuildingEntry();
    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Finish', 'FillPreventiveBaseMaintenanceBuildingEntry');

    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Start', 'FillPreventiveMP100MaintenanceBuildingEntry');
    CALL FillPreventiveMP100MaintenanceBuildingEntry();
    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Finish', 'FillPreventiveMP100MaintenanceBuildingEntry');

    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Start', 'FillPreventiveMP500MaintenanceBuildingEntry');
    CALL FillPreventiveMP500MaintenanceBuildingEntry();
    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Finish', 'FillPreventiveMP500MaintenanceBuildingEntry');

    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Start', 'FillPreventiveMP1200MaintenanceBuildingEntry');
    CALL FillPreventiveMP1200MaintenanceBuildingEntry();
    INSERT INTO lavomat.benchmark(timestamp, action, procedure)
    VALUES (NOW(), 'Finish', 'FillPreventiveMP1200MaintenanceBuildingEntry');
END
