DROP PROCEDURE IF EXISTS FillBuildingAverageUses;

DELIMITER //
CREATE PROCEDURE FillBuildingAverageUses()
BEGIN
	DELETE FROM average_uses WHERE id > 0;

	INSERT INTO average_uses(from_class, building_id, average30days, average6months, average1year)
	SELECT 'BuildingAverageUses', b.id, 
			CAST(IFNULL(thirtyDays.uses, 0)/(IFNULL(thirtyDays.thirtyWorkingDays, 1)*IFNULL(buildingMachines.machineCount, 1)) AS decimal(19,2)) AS thirtyDays, 
			CAST(IFNULL(sixMonths.uses, 0)/(IFNULL(sixMonths.sixMonthsWorkingDays, 1)*IFNULL(buildingMachines.machineCount, 1)) AS decimal(19,2)) AS sixMonths,  
			CAST(IFNULL(oneYear.uses, 0)/(IFNULL(oneYear.oneYearWorkingDays, 1)*IFNULL(buildingMachines.machineCount, 1)) AS decimal(19,2)) AS oneYear
	FROM building b 
	LEFT JOIN (SELECT bm.building_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS thirtyWorkingDays, COUNT(*) AS uses 
				FROM machine_use mu 
				INNER JOIN part p ON mu.machine_id = p.id 
				INNER JOIN building_machine bm ON p.id = bm.machine_id
				WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 30 DAY)  
				AND mu.result IN ('0', '1', '5', '6') 
				GROUP BY bm.building_id) thirtyDays 
	ON b.id = thirtyDays.building_id 
	LEFT JOIN (SELECT bm.building_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS sixMonthsWorkingDays, COUNT(*) AS uses 
				FROM machine_use mu 
				INNER JOIN part p ON mu.machine_id = p.id 
				INNER JOIN building_machine bm ON p.id = bm.machine_id
				WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 6 MONTH) 
				AND mu.result IN ('0', '1', '5', '6') 
				GROUP BY bm.building_id) sixMonths 
	ON b.id = sixMonths.building_id 
	LEFT JOIN (SELECT bm.building_id, DATEDIFF(NOW(), MIN(mu.timestamp)) AS oneYearWorkingDays, COUNT(*) AS uses 
				FROM machine_use mu 
				INNER JOIN part p ON mu.machine_id = p.id 
				INNER JOIN building_machine bm ON p.id = bm.machine_id
				WHERE mu.timestamp > DATE_SUB(NOW(), INTERVAL 1 YEAR) 
				AND mu.result IN ('0', '1', '5', '6') 
				GROUP BY bm.building_id) oneYear 
	ON b.id = oneYear.building_id
	LEFT JOIN (SELECT bm.building_id, COUNT(*) AS machineCount
			   FROM building_machine bm
			   GROUP BY bm.building_id) buildingMachines
	ON buildingMachines.building_id = b.id;

END
//
DELIMITER ;
