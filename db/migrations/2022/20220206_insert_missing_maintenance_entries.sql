INSERT INTO maintenance(technician, timestamp, building_id, maintenance_type)
SELECT 
	CASE 
		WHEN firstUse.machine_type = "WASHER" THEN "Primera Asignación Lavadora"
        WHEN firstUse.machine_type = "DRYER" THEN "Primera Asignación Secadora"
        ELSE ""
	END technician
    ,firstUse.timestamp
    ,b.id AS building_id
    ,b.mp AS maintenance_Type

FROM (SELECT b2.id, pivot.mp FROM building b2 CROSS JOIN (SELECT "MP100" AS mp UNION SELECT "MP300" AS mp UNION SELECT "MP1200" as mp) pivot) b

LEFT JOIN (SELECT p.building_id, p.machine_type, MIN(mu.timestamp) AS timestamp 
		   FROM machine_use mu
           INNER JOIN part p
           ON mu.machine_id = p.id
           WHERE p.machine_type IN ("WASHER", "DRYER")
           GROUP BY p.building_id, p.machine_type) firstUse
ON b.id = firstUse.building_id 

WHERE b.mp NOT IN (SELECT m100.maintenance_type
					FROM maintenance m100
                    WHERE m100.building_id = b.id
					GROUP BY m100.building_id, m100.maintenance_type)
AND ((firstUse.machine_type = "WASHER" AND b.mp IN ("MP100", "MP300"))
OR (firstUse.machine_type = "DRYER" AND b.mp IN ("MP1200")));
