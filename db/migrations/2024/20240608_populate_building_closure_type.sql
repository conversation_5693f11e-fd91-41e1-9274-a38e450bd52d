-- AFTER DEPLOY
UPDATE building b
    INNER JOIN rate r
    ON b.rate_id = r.id
    INNER JOIN rate_event re
    ON r.id = re.rate_id
SET b.closure_type = CONCAT(
        CASE
            WHEN b.invoicing_type = 'PER_UNIT' THEN 'PREPAID'
            WHEN b.invoicing_type = 'BUILDING' THEN 'POSTPAID'
            WHEN b.invoicing_type = 'HYBRID' THEN 'MIXED'
            ELSE ''
            END,
        CASE WHEN re.min_uses_per_washer > 0 THEN '_WITH_MINIMUM' ELSE '' END,
        CASE WHEN re.min_uses_per_unit > 0 THEN '_WITH_MINIMUM_PER_UNIT' ELSE '' END,
        CASE WHEN re.price_company <> re.price_customer THEN '_RESOURCE_REIMBURSEMENT' ELSE '' END
                     )
WHERE re.valid_from < NOW()
  AND re.valid_until > NOW()
  AND b.prepaid_rechargeable_uses = 0
  AND b.invoicing_type IS NOT NULL;
