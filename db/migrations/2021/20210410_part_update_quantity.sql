/* 
===================================== DOC =====================================

- File Location: db/migrations
- File name: <today date | 'YYYYMMdd'>_<table>_<action>_<column>.sql
- Template:
    -- BEFORE DEPLOY

    -- <What does the change do? | replace it with your comment>

    -- AFTER DEPLOY: <What did the deploy modify? | replace it with your comment>

    -- <What does the change do? | replace it with your comment>

================ Example: 20201126_parts_update_capacity.sql  ==================
*/

-- BEFORE DEPLOY

-- AFTER DEPLOY: Add capacity column to parts

--  Set default quantity column value for parts
UPDATE lavomat.part SET quantity=0 WHERE id > 0;
