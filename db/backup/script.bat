@echo off
rem credentials to connect to mysql server
set mysql_user=BACKUP_USER
set mysql_password=***********

rem path to backup compression utility
set seven_zip_path=C:\"Program Files"\7-Zip\

rem backup file name generation
set backup_name=lavomat-db-%date:~10,4%%date:~4,2%%date:~7,2%
set log_file=C:\backups\backup_error.log

rem clear any previous log
if exist %log_file% del %log_file%

rem backup creation
mysqldump --protocol=tcp --default-character-set=utf8 --host=lavomat.cq3icpiag9t0.us-west-2.rds.amazonaws.com --port=3306 --user=%mysql_user% --password=%mysql_password% --quick --lock-tables=false --set-gtid-purged=OFF --no-tablespaces "lavomat" > C:\backups\%backup_name%.sql 2>>%log_file%

if %ERRORLEVEL% neq 0 (
    set /p err_msg=<%log_file%
    eventcreate /ID 1 /L APPLICATION /T ERROR /SO mysql-backup-script /D "LAVOMAT - Backup failed during dump creation. Error: %err_msg%"
    exit /b 1
)

rem backup compression
"%seven_zip_path%7z" a C:\backups\%backup_name%.zip C:\backups\%backup_name%.sql 2>>%log_file%

if %ERRORLEVEL% neq 0 (
    set /p err_msg=<%log_file%
    eventcreate /ID 1 /L APPLICATION /T ERROR /SO mysql-backup-script /D "LAVOMAT - Backup failed during archive creation. Error: %err_msg%"
    exit /b 1
)

rem delete temporary .sql file
del C:\backups\%backup_name%.sql

eventcreate /ID 1 /L APPLICATION /T INFORMATION /SO mysql-backup-script /D "LAVOMAT - Backup successful"
